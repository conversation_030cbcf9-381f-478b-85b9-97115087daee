import { useState, useCallback } from "react";
import { <PERSON><PERSON><PERSON>t, <PERSON><PERSON>, Crown, Loader2, <PERSON><PERSON>les, RefreshCw } from "lucide-react";
import { Link } from "@tanstack/react-router";
import { But<PERSON> } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { Toaster } from "@/components/ui/toaster";
import { analysisService } from "@/lib/analysis-service";
import {
  BIO_ANALYSIS_STEPS,
  type BioAnalysisProgress,
  type BioAnalysisResult,
} from "@/types/analysis";

const BioScoreCard = ({ result }: { result: BioAnalysisResult }) => {
  const getScoreColor = (score: number) => {
    if (score >= 80) return "bg-gradient-primary";
    if (score >= 50) return "bg-warning-amber";
    return "bg-error-crimson";
  };

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden animate-fade-in">
      <div className="p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-semibold text-graphite-90">Bio Analysis</h3>
          <div
            className={`px-4 py-2 text-lg font-bold text-white rounded-full ${getScoreColor(result.overallScore)}`}
          >
            {result.overallScore}
          </div>
        </div>

        {/* Step Scores */}
        <div className="mb-6 space-y-2">
          {result.steps.map((step) => (
            <div key={step.stepId} className="flex justify-between items-center">
              <span className="text-graphite-60 font-medium">{step.stepName}</span>
              <div className="flex items-center gap-2">
                <span
                  className={`font-semibold ${step.score >= 70 ? "text-success-green" : step.score >= 50 ? "text-warning-amber" : "text-error-crimson"}`}
                >
                  {step.score}
                </span>
                <div className="w-16 h-2 bg-gray-200 rounded-full">
                  <div
                    className={`h-full rounded-full ${step.score >= 70 ? "bg-success-green" : step.score >= 50 ? "bg-warning-amber" : "bg-error-crimson"}`}
                    style={{ width: `${step.score}%` }}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Top Recommendations */}
        <div className="mb-4">
          <h4 className="font-semibold text-graphite-90 mb-2">Key Recommendations</h4>
          <ul className="space-y-2">
            {result.recommendations.slice(0, 3).map((rec, i) => (
              <li key={i} className="flex items-start text-sm text-graphite-60">
                <Sparkles className="h-4 w-4 mr-2 mt-0.5 text-flame-red flex-shrink-0" />
                <span>{rec}</span>
              </li>
            ))}
          </ul>
        </div>

        {result.error && (
          <div className="mt-3 p-3 bg-error-crimson/10 rounded text-sm text-error-crimson">
            {result.error}
          </div>
        )}
      </div>
    </div>
  );
};

const BioAnalyzingCard = ({ progress }: { progress?: BioAnalysisProgress }) => (
  <div className="bg-white rounded-lg shadow-md overflow-hidden">
    <div className="p-6">
      <div className="mb-4">
        <div className="h-6 w-3/4 rounded bg-gray-200 animate-pulse mb-2"></div>
        <div className="h-4 w-1/2 rounded bg-gray-200 animate-pulse"></div>
      </div>

      {progress && (
        <div className="space-y-3 mb-6">
          <div className="flex justify-between text-sm">
            <span className="text-graphite-60">Step {progress.currentStep}/5</span>
            <span className="text-flame-red font-medium">{Math.round(progress.progress)}%</span>
          </div>
          <div className="text-sm text-graphite-90 font-medium">{progress.stepName}</div>
          <Progress value={progress.progress} className="h-2" />
        </div>
      )}

      <div className="space-y-2">
        {BIO_ANALYSIS_STEPS.map((step) => (
          <div
            key={step.id}
            className={`flex items-center text-sm ${
              progress && progress.currentStep > step.id
                ? "text-success-green"
                : progress && progress.currentStep === step.id
                  ? "text-flame-red"
                  : "text-gray-400"
            }`}
          >
            <div
              className={`w-2 h-2 rounded-full mr-3 ${
                progress && progress.currentStep > step.id
                  ? "bg-success-green"
                  : progress && progress.currentStep === step.id
                    ? "bg-flame-red animate-pulse"
                    : "bg-gray-300"
              }`}
            />
            <span>{step.name}</span>
          </div>
        ))}
      </div>
    </div>
  </div>
);

const DiffViewer = ({ original, rewritten }: { original: string; rewritten: string }) => {
  const originalWords = original.split(/(\s+)/);

  return (
    <p className="text-body-md whitespace-pre-wrap">
      {rewritten.split(/(\s+)/).map((word, i) => {
        if (originalWords.includes(word)) {
          return <span key={i}>{word}</span>;
        } else {
          return (
            <span key={i} className="bg-success-green/20 text-success-green rounded px-1">
              {word}
            </span>
          );
        }
      })}
    </p>
  );
};

export function BioAnalyzer() {
  const [bio, setBio] = useState("");
  const [tone, setTone] = useState(1);
  const [result, setResult] = useState<BioAnalysisResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [status, setStatus] = useState<"idle" | "processing" | "done">("idle");
  const [progress, setProgress] = useState<BioAnalysisProgress | null>(null);
  const { toast } = useToast();

  const tones = ["Witty", "Sincere", "Adventurous"] as const;
  const toneValues: Record<number, "witty" | "sincere" | "adventurous"> = {
    0: "witty",
    1: "sincere",
    2: "adventurous",
  };

  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied to clipboard!",
      description: "The bio is now ready to paste.",
    });
  };

  const analyzeBio = useCallback(async () => {
    if (bio.length < 20) {
      setError("Bio must be at least 20 characters.");
      return;
    }
    if (bio.length > 500) {
      setError("Bio must be 500 characters or less.");
      return;
    }

    setStatus("processing");
    setError(null);
    setProgress(null);

    try {
      await analysisService.analyzeBio(bio, toneValues[tone], {
        onProgress: (progressData) => {
          setProgress(progressData);
        },
        onComplete: (analysisResult) => {
          setResult(analysisResult);
          setStatus("done");
        },
        onError: (errorMessage) => {
          setError(errorMessage);
          setStatus("idle");
        },
      });
    } catch (error) {
      console.error("Bio analysis failed:", error);
      setError("Analysis failed. Please try again.");
      setStatus("idle");
    }
  }, [bio, tone]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    analyzeBio();
  };

  const handleReset = () => {
    setStatus("idle");
    setResult(null);
    setProgress(null);
    setError(null);
  };

  return (
    <>
      <Toaster />
      <div className="min-h-screen bg-gray-50">
        <header className="bg-cloud-white shadow-sm sticky top-0 z-10">
          <div className="container mx-auto px-4 md:px-6 h-16 flex items-center justify-between">
            <Link
              to="/image-analyzer"
              className="flex items-center gap-2 text-graphite-60 hover:text-graphite-90"
            >
              <ArrowLeft className="h-5 w-5" />
              <span className="font-semibold">Back to Images</span>
            </Link>
          </div>
          {status === "processing" && progress && (
            <div className="h-1 bg-gray-200">
              <div
                className="h-full bg-gradient-primary transition-all duration-300"
                style={{ width: `${progress.progress}%` }}
              />
            </div>
          )}
        </header>

        <main className="container mx-auto px-4 md:px-6 py-8">
          <div className="max-w-4xl mx-auto">
            <div className="text-center">
              <h1 className="text-h2-mobile md:text-h2">Bio Analyzer</h1>
              <p className="text-body-lg text-graphite-60 mt-2">
                Get AI-powered analysis and optimization for your dating bio.
              </p>

              <div className="flex justify-center gap-2 mt-4">
                <Badge variant="secondary" className="flex items-center gap-1">
                  <Sparkles className="h-3 w-3" />
                  Multi-step AI analysis
                </Badge>
                <Badge variant="outline" className="flex items-center gap-1">
                  <RefreshCw className="h-3 w-3" />
                  Personalized improvements
                </Badge>
              </div>
            </div>

            {status !== "done" && (
              <div className="mt-8 max-w-2xl mx-auto">
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div>
                    <Textarea
                      placeholder="Paste your current bio here..."
                      value={bio}
                      onChange={(e) => setBio(e.target.value)}
                      className="min-h-[160px] md:min-h-[200px] bg-white"
                      maxLength={500}
                      required
                      disabled={status === "processing"}
                    />
                    <p className="text-right text-caption text-graphite-60 mt-1">
                      {bio.length} / 500
                    </p>
                  </div>

                  <div>
                    <label className="font-semibold text-body-md">Select Improvement Tone</label>
                    <div className="mt-4">
                      <Slider
                        value={[tone]}
                        onValueChange={(value) => setTone(value[0])}
                        max={2}
                        step={1}
                        className="w-full"
                        disabled={status === "processing"}
                      />
                      <div className="flex justify-between text-caption text-graphite-60 mt-2">
                        {tones.map((t) => (
                          <span key={t}>{t}</span>
                        ))}
                      </div>
                    </div>
                  </div>

                  <Button
                    type="submit"
                    size="lg"
                    disabled={bio.length < 20 || status === "processing"}
                    className="w-full"
                  >
                    {status === "processing" ? (
                      <>
                        <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                        Analyzing with AI...
                      </>
                    ) : (
                      "Analyze Bio with AI"
                    )}
                  </Button>
                </form>

                {error && <p className="mt-4 text-center text-error-crimson">{error}</p>}
              </div>
            )}

            {status === "processing" && (
              <div className="mt-12 max-w-2xl mx-auto">
                <BioAnalyzingCard progress={progress || undefined} />
              </div>
            )}

            {status === "done" && result && (
              <div className="mt-12">
                <div className="text-center mb-6">
                  <h2 className="text-xl font-semibold">Analysis Complete</h2>
                  <p className="text-graphite-60 mt-2">
                    Your bio has been analyzed using our 5-step AI assessment
                  </p>
                </div>

                <div className="grid lg:grid-cols-2 gap-8">
                  <BioScoreCard result={result} />

                  <div className="space-y-6">
                    <Tabs defaultValue="original" className="w-full">
                      <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="original">Original Bio</TabsTrigger>
                        <TabsTrigger value="improved" disabled={!result.improvedBio}>
                          AI Improved
                        </TabsTrigger>
                      </TabsList>
                      <TabsContent value="original" className="mt-4 p-6 bg-white rounded-lg border">
                        <div className="mb-4">
                          <h4 className="font-semibold text-graphite-90 mb-2">Your Original Bio</h4>
                        </div>
                        <p className="text-body-md whitespace-pre-wrap text-graphite-70">
                          {result.originalBio}
                        </p>
                        <div className="mt-4">
                          <Button
                            onClick={() => handleCopy(result.originalBio)}
                            size="sm"
                            variant="secondary"
                          >
                            <Copy className="mr-2 h-4 w-4" /> Copy Original
                          </Button>
                        </div>
                      </TabsContent>
                      {result.improvedBio && (
                        <TabsContent
                          value="improved"
                          className="mt-4 p-6 bg-white rounded-lg border"
                        >
                          <div className="mb-4">
                            <h4 className="font-semibold text-graphite-90 mb-2">AI-Improved Bio</h4>
                            <Badge variant="secondary">{tones[tone]} tone</Badge>
                          </div>
                          <DiffViewer
                            original={result.originalBio}
                            rewritten={result.improvedBio}
                          />
                          <div className="mt-4">
                            <Button onClick={() => handleCopy(result.improvedBio!)} size="sm">
                              <Copy className="mr-2 h-4 w-4" /> Copy Improved
                            </Button>
                          </div>
                        </TabsContent>
                      )}
                    </Tabs>
                  </div>
                </div>

                <div className="mt-8 text-center space-y-4">
                  <div className="flex justify-center space-x-4">
                    <Button onClick={handleReset} variant="secondary">
                      Analyze Another Bio
                    </Button>
                    <Button asChild size="lg">
                      <Link to="/image-analyzer">
                        Analyze Photos Next <Sparkles className="ml-2 h-5 w-5" />
                      </Link>
                    </Button>
                  </div>

                  <div className="pt-4 border-t">
                    <p className="text-sm text-gray-600 mb-3">Want professional-grade analysis?</p>
                    <Button
                      asChild
                      variant="outline"
                      className="border-purple-300 text-purple-700 hover:bg-purple-50"
                    >
                      <Link to="/bio-analyzer-pro">
                        Try Advanced Analysis <Crown className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </main>
      </div>
    </>
  );
}
