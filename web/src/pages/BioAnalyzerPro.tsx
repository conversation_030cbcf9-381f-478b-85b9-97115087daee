import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>ader<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, User } from "lucide-react";
import { useState } from "react";
import { <PERSON> } from "@tanstack/react-router";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>L<PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { AdvancedBioAnalyzer } from "@/lib/advanced/bio-analyzer-pro";
import type {
  AdvancedBioAnalysisResult,
  AdvancedAnalysisProgress,
} from "@/lib/advanced/types/advanced-analysis";

type AnalysisStatus = "idle" | "processing" | "done";

export default function BioAnalyzerPro() {
  const [bio, setBio] = useState("");
  const [status, setStatus] = useState<AnalysisStatus>("idle");
  const [result, setResult] = useState<AdvancedBioAnalysisResult | null>(null);
  const [progress, setProgress] = useState<AdvancedAnalysisProgress | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [analyzer] = useState(() => new AdvancedBioAnalyzer());

  const analyzeBio = async () => {
    if (bio.length < 20) {
      setError("Bio must be at least 20 characters.");
      return;
    }
    if (bio.length > 500) {
      setError("Bio must be 500 characters or less.");
      return;
    }

    setStatus("processing");
    setError(null);
    setProgress(null);

    try {
      const analysisResult = await analyzer.analyzeBio(
        bio,
        {
          analysisDepth: "comprehensive",
          includeComparative: true,
          generateImprovements: true,
        },
        (progressData) => {
          setProgress(progressData);
        }
      );

      setResult(analysisResult);
      setStatus("done");
    } catch (error) {
      console.error("Advanced bio analysis failed:", error);
      setError("Analysis failed. Please try again.");
      setStatus("idle");
    }
  };

  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
      <header className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="sm" asChild>
                <Link to="/bio-analyzer">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Basic
                </Link>
              </Button>
              <div className="flex items-center space-x-2">
                <Crown className="h-6 w-6 text-purple-600" />
                <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                  Advanced Bio Analyzer
                </h1>
                <Badge variant="secondary" className="bg-purple-100 text-purple-700">
                  Powered by OpenRouter o3
                </Badge>
              </div>
            </div>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        {status === "idle" && (
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Professional Bio Analysis & Optimization
              </h2>
              <p className="text-lg text-gray-600 mb-6">
                Get expert-level insights and improvements powered by OpenRouter's o3 model
              </p>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <Card>
                  <CardHeader className="text-center">
                    <Brain className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                    <CardTitle className="text-lg">Psychological Profiling</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-gray-600">
                      Big 5 personality traits, attachment style, and emotional intelligence
                      analysis
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="text-center">
                    <TrendingUp className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                    <CardTitle className="text-lg">Market Analysis</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-gray-600">
                      Target audience alignment, competitive positioning, and conversion potential
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="text-center">
                    <Sparkles className="h-8 w-8 text-green-600 mx-auto mb-2" />
                    <CardTitle className="text-lg">AI Improvements</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-gray-600">
                      Three optimized versions in different tones with expert recommendations
                    </p>
                  </CardContent>
                </Card>
              </div>
            </div>

            <Card className="max-w-2xl mx-auto">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <User className="h-5 w-5" />
                  <span>Enter Your Bio</span>
                </CardTitle>
                <CardDescription>
                  Paste your current dating bio for comprehensive analysis
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <Textarea
                      placeholder="Enter your dating bio here..."
                      value={bio}
                      onChange={(e) => setBio(e.target.value)}
                      className="min-h-[120px] resize-none"
                      maxLength={500}
                    />
                    <div className="flex justify-between text-sm text-gray-500 mt-2">
                      <span>Minimum 20 characters</span>
                      <span>{bio.length}/500</span>
                    </div>
                  </div>

                  <Button
                    onClick={analyzeBio}
                    size="lg"
                    disabled={bio.length < 20}
                    className="w-full bg-gradient-to-r from-purple-600 to-blue-600"
                  >
                    <Brain className="mr-2 h-5 w-5" />
                    Analyze with Advanced AI
                  </Button>

                  {error && <p className="text-center text-red-600 text-sm">{error}</p>}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {status === "processing" && (
          <div className="max-w-2xl mx-auto text-center">
            <div className="mb-8">
              <Crown className="h-16 w-16 text-purple-600 mx-auto mb-4 animate-pulse" />
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Advanced Analysis in Progress
              </h2>
              <p className="text-gray-600">
                Our expert AI system is analyzing your bio with professional-grade precision
              </p>
            </div>

            {progress && (
              <div className="space-y-4">
                <div className="bg-white rounded-lg p-6 shadow-sm border">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium text-gray-900">{progress.message}</span>
                    <span className="text-sm text-gray-500">{Math.round(progress.progress)}%</span>
                  </div>
                  <Progress value={progress.progress} className="h-2" />

                  {progress.currentExpert && (
                    <p className="text-sm text-gray-600 mt-2">
                      Current Expert: {progress.currentExpert}
                    </p>
                  )}
                </div>

                <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Processing with OpenRouter o3 model...</span>
                </div>
              </div>
            )}
          </div>
        )}

        {status === "done" && result && (
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-2">Advanced Analysis Complete</h2>
              <p className="text-gray-600">
                Professional insights and optimized versions of your bio
              </p>
            </div>

            <Card className="mb-8">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Analysis Results</CardTitle>
                  <div className="flex items-center space-x-4">
                    <Badge variant="outline" className="text-lg font-bold">
                      {result.overallScore}/100
                    </Badge>
                    <Badge variant="secondary">{result.percentileRank}th percentile</Badge>
                  </div>
                </div>
              </CardHeader>

              <CardContent>
                <Tabs defaultValue="overview" className="w-full">
                  <TabsList className="grid w-full grid-cols-6">
                    <TabsTrigger value="overview">Overview</TabsTrigger>
                    <TabsTrigger value="experts">Expert Analysis</TabsTrigger>
                    <TabsTrigger value="psychology">Psychology</TabsTrigger>
                    <TabsTrigger value="market">Market</TabsTrigger>
                    <TabsTrigger value="improvements">Improvements</TabsTrigger>
                    <TabsTrigger value="comparison">Comparison</TabsTrigger>
                  </TabsList>

                  <TabsContent value="overview" className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm">Overall Score</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold text-purple-600">
                            {result.overallScore}/100
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm">Percentile Rank</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold text-blue-600">
                            {result.percentileRank}th
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm">Improvement Potential</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold text-green-600">
                            {result.improvementPotential}%
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm">Market Competitiveness</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold text-orange-600">
                            {result.marketCompetitiveness}/100
                          </div>
                        </CardContent>
                      </Card>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-lg">Quick Wins</CardTitle>
                          <CardDescription>Easy improvements with high impact</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-3">
                            {result.quickWins.slice(0, 3).map((rec, i) => (
                              <div key={i} className="flex items-start space-x-3">
                                <Badge variant="outline" className="text-xs">
                                  {rec.impactScore}
                                </Badge>
                                <p className="text-sm">{rec.recommendation}</p>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader>
                          <CardTitle className="text-lg">Long-term Improvements</CardTitle>
                          <CardDescription>Strategic changes for maximum impact</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-3">
                            {result.longTermImprovements.slice(0, 3).map((rec, i) => (
                              <div key={i} className="flex items-start space-x-3">
                                <Badge variant="outline" className="text-xs">
                                  {rec.impactScore}
                                </Badge>
                                <p className="text-sm">{rec.recommendation}</p>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </TabsContent>

                  <TabsContent value="experts" className="space-y-4">
                    {result.expertAnalyses.map((expert, i) => (
                      <Card key={i}>
                        <CardHeader>
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-lg capitalize">
                              {expert.expertType.replace("_", " ")} Expert
                            </CardTitle>
                            <div className="flex items-center space-x-2">
                              <Badge variant="outline">{expert.score}/100</Badge>
                              <Badge variant="secondary">{expert.confidence}% confident</Badge>
                            </div>
                          </div>
                          <CardDescription>{expert.credentials}</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm text-gray-700 mb-4">{expert.analysis}</p>
                          <div className="space-y-2">
                            <h4 className="font-medium text-sm">Key Observations:</h4>
                            <ul className="text-sm text-gray-600 space-y-1">
                              {expert.keyObservations.map((obs, j) => (
                                <li key={j} className="flex items-start space-x-2">
                                  <span className="text-purple-500">•</span>
                                  <span>{obs}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </TabsContent>

                  <TabsContent value="psychology" className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <Card>
                        <CardHeader>
                          <CardTitle>Personality Profile (Big 5)</CardTitle>
                          <CardDescription>Psychological trait analysis</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-4">
                            {Object.entries(result.psychologicalProfile.personalityTraits).map(
                              ([trait, score]) => (
                                <div key={trait}>
                                  <div className="flex justify-between text-sm mb-1">
                                    <span className="capitalize font-medium">{trait}</span>
                                    <span>{score}/100</span>
                                  </div>
                                  <Progress value={score} className="h-2" />
                                </div>
                              )
                            )}
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader>
                          <CardTitle>Psychological Indicators</CardTitle>
                          <CardDescription>Key psychological metrics</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-4">
                            <div>
                              <div className="flex justify-between text-sm mb-1">
                                <span className="font-medium">Attachment Style</span>
                                <span className="capitalize">
                                  {result.psychologicalProfile.attachmentStyle}
                                </span>
                              </div>
                            </div>

                            <div>
                              <div className="flex justify-between text-sm mb-1">
                                <span className="font-medium">Confidence Level</span>
                                <span>{result.psychologicalProfile.confidenceLevel}/100</span>
                              </div>
                              <Progress
                                value={result.psychologicalProfile.confidenceLevel}
                                className="h-2"
                              />
                            </div>

                            <div>
                              <div className="flex justify-between text-sm mb-1">
                                <span className="font-medium">Emotional Intelligence</span>
                                <span>{result.psychologicalProfile.emotionalIntelligence}/100</span>
                              </div>
                              <Progress
                                value={result.psychologicalProfile.emotionalIntelligence}
                                className="h-2"
                              />
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>

                    <Card>
                      <CardHeader>
                        <CardTitle>Linguistic Analysis</CardTitle>
                        <CardDescription>Communication style assessment</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                          <div>
                            <h4 className="font-medium text-sm mb-2">Readability</h4>
                            <div className="text-2xl font-bold text-blue-600 mb-1">
                              {result.linguisticAnalysis.readabilityScore}/100
                            </div>
                            <Progress
                              value={result.linguisticAnalysis.readabilityScore}
                              className="h-2"
                            />
                          </div>

                          <div>
                            <h4 className="font-medium text-sm mb-2">Sentiment</h4>
                            <div className="text-2xl font-bold text-green-600 mb-1">
                              {result.linguisticAnalysis.sentimentScore}/100
                            </div>
                            <Progress
                              value={result.linguisticAnalysis.sentimentScore}
                              className="h-2"
                            />
                          </div>

                          <div>
                            <h4 className="font-medium text-sm mb-2">Grammar</h4>
                            <div className="text-2xl font-bold text-purple-600 mb-1">
                              {result.linguisticAnalysis.grammarScore}/100
                            </div>
                            <Progress
                              value={result.linguisticAnalysis.grammarScore}
                              className="h-2"
                            />
                          </div>
                        </div>

                        <div className="mt-6">
                          <h4 className="font-medium text-sm mb-2">Tone Analysis:</h4>
                          <div className="flex flex-wrap gap-2">
                            {result.linguisticAnalysis.toneAnalysis.map((tone, i) => (
                              <Badge key={i} variant="outline" className="text-xs">
                                {tone}
                              </Badge>
                            ))}
                          </div>

                          <div className="mt-4">
                            <span className="font-medium text-sm">Vocabulary Level: </span>
                            <Badge variant="secondary" className="capitalize">
                              {result.linguisticAnalysis.vocabularyLevel}
                            </Badge>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  <TabsContent value="market" className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <Card>
                        <CardHeader>
                          <CardTitle>Market Performance</CardTitle>
                          <CardDescription>Dating market analysis</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-4">
                            <div>
                              <div className="flex justify-between text-sm mb-1">
                                <span className="font-medium">Target Audience Alignment</span>
                                <span>{result.marketAnalysis.targetAudienceAlignment}/100</span>
                              </div>
                              <Progress
                                value={result.marketAnalysis.targetAudienceAlignment}
                                className="h-2"
                              />
                            </div>

                            <div>
                              <div className="flex justify-between text-sm mb-1">
                                <span className="font-medium">Conversion Potential</span>
                                <span>{result.marketAnalysis.conversionPotential}/100</span>
                              </div>
                              <Progress
                                value={result.marketAnalysis.conversionPotential}
                                className="h-2"
                              />
                            </div>

                            <div>
                              <div className="flex justify-between text-sm mb-1">
                                <span className="font-medium">Engagement Probability</span>
                                <span>{result.marketAnalysis.engagementProbability}/100</span>
                              </div>
                              <Progress
                                value={result.marketAnalysis.engagementProbability}
                                className="h-2"
                              />
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader>
                          <CardTitle>Market Positioning</CardTitle>
                          <CardDescription>Competitive analysis</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-4">
                            <div>
                              <h4 className="font-medium text-sm mb-2">Competitive Position:</h4>
                              <Badge variant="outline" className="text-lg capitalize">
                                {result.marketAnalysis.competitivePositioning}
                              </Badge>
                            </div>

                            <div>
                              <h4 className="font-medium text-sm mb-2">Niche Appeal:</h4>
                              <div className="flex flex-wrap gap-2">
                                {result.marketAnalysis.nicheAppeal.map((niche, i) => (
                                  <Badge key={i} variant="secondary" className="text-xs">
                                    {niche}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </TabsContent>

                  <TabsContent value="improvements" className="space-y-6">
                    <div className="grid grid-cols-1 gap-6">
                      <Card>
                        <CardHeader>
                          <CardTitle>Original Bio</CardTitle>
                          <CardDescription>Your current bio for reference</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="bg-gray-50 p-4 rounded-lg mb-4">
                            <p className="text-sm whitespace-pre-wrap">{result.originalBio}</p>
                          </div>
                          <Button
                            onClick={() => handleCopy(result.originalBio)}
                            size="sm"
                            variant="outline"
                          >
                            <Copy className="mr-2 h-4 w-4" /> Copy Original
                          </Button>
                        </CardContent>
                      </Card>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        {Object.entries(result.improvedVersions).map(([tone, improvedBio]) => (
                          <Card key={tone}>
                            <CardHeader>
                              <CardTitle className="capitalize">{tone} Version</CardTitle>
                              <CardDescription>AI-optimized bio in {tone} tone</CardDescription>
                            </CardHeader>
                            <CardContent>
                              <div className="bg-gray-50 p-4 rounded-lg mb-4 min-h-[120px]">
                                <p className="text-sm whitespace-pre-wrap">{improvedBio}</p>
                              </div>
                              <Button
                                onClick={() => handleCopy(improvedBio)}
                                size="sm"
                                variant="outline"
                                className="w-full"
                              >
                                <Copy className="mr-2 h-4 w-4" /> Copy {tone}
                              </Button>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="comparison" className="space-y-4">
                    <Card>
                      <CardHeader>
                        <CardTitle>Market Comparison</CardTitle>
                        <CardDescription>How your bio compares to others</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <h4 className="font-medium text-sm mb-4">Market Position</h4>
                            <div className="text-center p-6 bg-gray-50 rounded-lg">
                              <div className="text-3xl font-bold text-purple-600 mb-2">
                                {result.comparativeAnalysis.marketPosition
                                  .replace("_", " ")
                                  .toUpperCase()}
                              </div>
                              <p className="text-sm text-gray-600">
                                Top {result.comparativeAnalysis.topPercentile}% of bios
                              </p>
                            </div>
                          </div>

                          <div>
                            <h4 className="font-medium text-sm mb-4">Competitive Advantages</h4>
                            <div className="space-y-2">
                              {result.comparativeAnalysis.competitiveAdvantages.map(
                                (advantage, i) => (
                                  <div key={i} className="flex items-center space-x-2">
                                    <span className="text-green-500">✓</span>
                                    <span className="text-sm">{advantage}</span>
                                  </div>
                                )
                              )}
                            </div>

                            <h4 className="font-medium text-sm mb-2 mt-4">Areas for Improvement</h4>
                            <div className="space-y-2">
                              {result.comparativeAnalysis.areasForImprovement.map((area, i) => (
                                <div key={i} className="flex items-center space-x-2">
                                  <span className="text-orange-500">→</span>
                                  <span className="text-sm">{area}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>

            <div className="text-center">
              <Button
                variant="outline"
                onClick={() => {
                  setStatus("idle");
                  setResult(null);
                  setBio("");
                }}
                className="mr-4"
              >
                Analyze Another Bio
              </Button>
              <Button asChild size="lg">
                <Link to="/image-analyzer-pro">
                  Analyze Photos with Advanced AI <Sparkles className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
