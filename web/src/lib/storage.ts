// IndexedDB utilities for temporary image storage
const DB_NAME = "TinderOpImageDB";
const DB_VERSION = 1;
const STORE_NAME = "images";
const SESSION_KEY = "tinderop_session";

export interface StoredImage {
  id: string;
  fileName: string;
  blob: Blob;
  mimeType: string;
  uploadedAt: number;
  sessionId: string;
  // Optional fields for edited images
  isEdited?: boolean;
  originalImageId?: string;
  editPrompt?: string;
  editedAt?: number;
  originalImageBlob?: Blob; // Store original image for before/after comparison
}

export interface EditHistory {
  originalImageId: string;
  editedVersions: Array<{
    id: string;
    prompt: string;
    editedAt: number;
    processingTime: number;
  }>;
}

export interface AnalysisSession {
  id: string;
  createdAt: number;
  imageIds: string[];
  results: Record<string, any>;
}

class ImageStorage {
  private db: IDBDatabase | null = null;

  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(DB_NAME, DB_VERSION);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        if (!db.objectStoreNames.contains(STORE_NAME)) {
          const store = db.createObjectStore(STORE_NAME, { keyPath: "id" });
          store.createIndex("sessionId", "sessionId", { unique: false });
          store.createIndex("uploadedAt", "uploadedAt", { unique: false });
        }
      };
    });
  }

  async storeImage(file: File, sessionId: string): Promise<string> {
    if (!this.db) await this.init();

    const id = `${sessionId}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const storedImage: StoredImage = {
      id,
      fileName: file.name,
      blob: file,
      mimeType: file.type,
      uploadedAt: Date.now(),
      sessionId,
    };

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([STORE_NAME], "readwrite");
      const store = transaction.objectStore(STORE_NAME);
      const request = store.add(storedImage);

      request.onsuccess = () => resolve(id);
      request.onerror = () => reject(request.error);
    });
  }

  async getImage(id: string): Promise<StoredImage | null> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([STORE_NAME], "readonly");
      const store = transaction.objectStore(STORE_NAME);
      const request = store.get(id);

      request.onsuccess = () => resolve(request.result || null);
      request.onerror = () => reject(request.error);
    });
  }

  async getSessionImages(sessionId: string): Promise<StoredImage[]> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([STORE_NAME], "readonly");
      const store = transaction.objectStore(STORE_NAME);
      const index = store.index("sessionId");
      const request = index.getAll(sessionId);

      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  async deleteImage(id: string): Promise<void> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([STORE_NAME], "readwrite");
      const store = transaction.objectStore(STORE_NAME);
      const request = store.delete(id);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  async storeEditedImage(
    editedBlob: Blob,
    originalImageId: string,
    editPrompt: string,
    sessionId: string,
    originalImageBlob?: Blob
  ): Promise<string> {
    if (!this.db) await this.init();

    const id = `edited_${originalImageId}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const storedImage: StoredImage = {
      id,
      fileName: `edited_${Date.now()}.png`,
      blob: editedBlob,
      mimeType: "image/png",
      uploadedAt: Date.now(),
      sessionId,
      isEdited: true,
      originalImageId,
      editPrompt,
      editedAt: Date.now(),
      originalImageBlob, // Store original for before/after comparison
    };

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([STORE_NAME], "readwrite");
      const store = transaction.objectStore(STORE_NAME);
      const request = store.add(storedImage);

      request.onsuccess = () => resolve(id);
      request.onerror = () => reject(request.error);
    });
  }

  async getEditedVersions(originalImageId: string): Promise<StoredImage[]> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([STORE_NAME], "readonly");
      const store = transaction.objectStore(STORE_NAME);
      const request = store.getAll();

      request.onsuccess = () => {
        const allImages = request.result;
        const editedVersions = allImages.filter(
          (img) => img.isEdited && img.originalImageId === originalImageId
        );
        resolve(editedVersions);
      };
      request.onerror = () => reject(request.error);
    });
  }

  async clearSession(sessionId: string): Promise<void> {
    if (!this.db) await this.init();

    const images = await this.getSessionImages(sessionId);
    const deletePromises = images.map((img) => this.deleteImage(img.id));
    await Promise.all(deletePromises);
  }

  async cleanup(): Promise<void> {
    if (!this.db) await this.init();

    const cutoff = Date.now() - 24 * 60 * 60 * 1000; // 24 hours ago

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([STORE_NAME], "readwrite");
      const store = transaction.objectStore(STORE_NAME);
      const index = store.index("uploadedAt");
      const request = index.openCursor(IDBKeyRange.upperBound(cutoff));

      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result;
        if (cursor) {
          cursor.delete();
          cursor.continue();
        } else {
          resolve();
        }
      };
      request.onerror = () => reject(request.error);
    });
  }

  // Global storage management functions
  async getAllOriginalImages(): Promise<StoredImage[]> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([STORE_NAME], "readonly");
      const store = transaction.objectStore(STORE_NAME);
      const request = store.getAll();

      request.onsuccess = () => {
        const allImages = request.result as StoredImage[];
        // Filter only original images (not edited versions) and sort by upload time
        const originalImages = allImages
          .filter(img => !img.isEdited)
          .sort((a, b) => a.uploadedAt - b.uploadedAt); // Oldest first

        console.log(`📊 Global Storage: Found ${originalImages.length} original images`);
        resolve(originalImages);
      };

      request.onerror = () => reject(request.error);
    });
  }

  async getGlobalOriginalCount(): Promise<number> {
    const originalImages = await this.getAllOriginalImages();
    return originalImages.length;
  }

  async enforceGlobalOriginalLimit(maxImages: number): Promise<void> {
    console.log(`🔧 Storage: Enforcing global limit of ${maxImages} original images`);

    const originalImages = await this.getAllOriginalImages();
    const currentCount = originalImages.length;

    if (currentCount <= maxImages) {
      console.log(`✅ Storage: Current count (${currentCount}) within limit (${maxImages})`);
      return;
    }

    // Calculate how many images to remove
    const imagesToRemove = currentCount - maxImages;
    const oldestImages = originalImages.slice(0, imagesToRemove);

    console.log(`🗑️ Storage: Removing ${imagesToRemove} oldest images to enforce limit`);

    // Remove oldest images and their associated enhanced versions
    for (const image of oldestImages) {
      console.log(`🗑️ Storage: Removing original image: ${image.fileName} (${image.id})`);

      // Find and remove all enhanced versions of this original image
      const enhancedVersions = await this.getEnhancedVersions(image.id);
      for (const enhanced of enhancedVersions) {
        console.log(`🗑️ Storage: Removing enhanced version: ${enhanced.id}`);
        await this.deleteImage(enhanced.id);
      }

      // Remove the original image
      await this.deleteImage(image.id);

      // Remove from session management
      sessionManager.removeImageFromSession(image.id);
    }

    console.log(`✅ Storage: Successfully enforced global limit. Removed ${imagesToRemove} images`);
  }

  async getEnhancedVersions(originalImageId: string): Promise<StoredImage[]> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([STORE_NAME], "readonly");
      const store = transaction.objectStore(STORE_NAME);
      const request = store.getAll();

      request.onsuccess = () => {
        const allImages = request.result as StoredImage[];
        const enhancedVersions = allImages.filter(
          img => img.isEdited && img.originalImageId === originalImageId
        );
        resolve(enhancedVersions);
      };

      request.onerror = () => reject(request.error);
    });
  }
}

// Session management using localStorage
export class SessionManager {
  private static instance: SessionManager;
  private currentSession: AnalysisSession | null = null;

  static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager();
    }
    return SessionManager.instance;
  }

  getCurrentSession(): AnalysisSession {
    if (this.currentSession) return this.currentSession;

    const stored = localStorage.getItem(SESSION_KEY);
    if (stored) {
      try {
        this.currentSession = JSON.parse(stored);
        return this.currentSession!;
      } catch (e) {
        console.warn("Failed to parse stored session, creating new one");
      }
    }

    this.currentSession = {
      id: `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      createdAt: Date.now(),
      imageIds: [],
      results: {},
    };

    this.saveSession();
    return this.currentSession;
  }

  addImageToSession(imageId: string): void {
    const session = this.getCurrentSession();
    if (!session.imageIds.includes(imageId)) {
      session.imageIds.push(imageId);
      this.saveSession();
    }
  }

  removeImageFromSession(imageId: string): void {
    const session = this.getCurrentSession();
    session.imageIds = session.imageIds.filter((id) => id !== imageId);
    delete session.results[imageId];
    this.saveSession();
  }

  saveAnalysisResult(imageId: string, result: any): void {
    const session = this.getCurrentSession();
    session.results[imageId] = result;
    this.saveSession();
  }

  getAnalysisResult(imageId: string): any {
    const session = this.getCurrentSession();
    return session.results[imageId];
  }

  clearSession(): void {
    localStorage.removeItem(SESSION_KEY);
    this.currentSession = null;
  }

  private saveSession(): void {
    if (this.currentSession) {
      localStorage.setItem(SESSION_KEY, JSON.stringify(this.currentSession));
    }
  }
}

// Global instances
export const imageStorage = new ImageStorage();
export const sessionManager = SessionManager.getInstance();

// Utility functions
export async function convertBlobToBase64(blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const result = reader.result as string;
      resolve(result.split(",")[1]); // Remove data:image/jpeg;base64, prefix
    };
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
}

export function createImagePreview(blob: Blob): string {
  return URL.createObjectURL(blob);
}

export function revokeImagePreview(url: string): void {
  URL.revokeObjectURL(url);
}

// Initialize cleanup on app start
export async function initStorage(): Promise<void> {
  await imageStorage.init();
  await imageStorage.cleanup(); // Clean up old images
}
