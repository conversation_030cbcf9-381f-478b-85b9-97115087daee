/// <reference types="vite/client" />

import OpenAI, { toFile } from "openai";

export interface EditResult {
  editedImageUrl: string;
  editedImageBlob: Blob;
  prompt: string;
  processingTime: number;
  success: boolean;
  error?: string;
}

export interface StreamingEditResult {
  partialImageUrl?: string;
  partialImageBlob?: Blob;
  progress: number;
  status: string;
  isComplete: boolean;
  finalResult?: EditResult;
}

export class ImageEditingService {
  private apiKey: string;
  private client: OpenAI;

  constructor() {
    // Get API key from environment variables
    this.apiKey = import.meta.env.VITE_OPENAI_API_KEY;

    if (!this.apiKey) {
      console.error(
        "🔑 VITE_OPENAI_API_KEY not found in environment variables"
      );
      throw new Error(
        "OpenAI API key is required. Please set VITE_OPENAI_API_KEY in your .env file"
      );
    }

    console.log("🔑 OpenAI API key loaded successfully");
    console.log(`🔑 API Key preview: ${this.apiKey.substring(0, 10)}...`);

    // Initialize OpenAI client
    this.client = new OpenAI({
      apiKey: this.apiKey,
      dangerouslyAllowBrowser: true, // Required for client-side usage
    });
  }

  async *editSingleImageStream(
    imageBlob: Blob,
    prompt: string
  ): AsyncGenerator<StreamingEditResult, EditResult, unknown> {
    const startTime = Date.now();

    try {
      console.log("🎨 Starting streaming image edit with prompt:", prompt);

      // Convert blob to File object for OpenAI
      yield {
        progress: 5,
        status: "Preparing image for streaming edit...",
        isComplete: false,
      };

      const imageFile = await toFile(imageBlob, "image.png", {
        type: imageBlob.type || "image/png",
      });

      yield {
        progress: 10,
        status: "Connecting to OpenAI streaming API...",
        isComplete: false,
      };

      // Progress steps for streaming simulation
      const progressSteps = [
        { progress: 8, status: "Analyzing image composition...", delay: 10000 },
        {
          progress: 41,
          status: "AI is processing your image...",
          delay: 40000,
        },
        {
          progress: 45,
          status: "Applying enhancement algorithms...",
          delay: 5000,
        },
        {
          progress: 49,
          status: "Processing lighting adjustments...",
          delay: 5000,
        },
        { progress: 54, status: "Refining image details...", delay: 6000 },
        {
          progress: 61,
          status: "Enhancing colors and contrast...",
          delay: 7000,
        },
        { progress: 67, status: "Finalizing enhancements...", delay: 8000 },
        { progress: 92, status: "Generating final image...", delay: 30000 },
        { progress: 92.5, status: "Converting edited image...", delay: 622 },
      ];

      // Start the actual API call
      const apiPromise = this.client.images.edit({
        model: "gpt-image-1",
        image: [imageFile],
        prompt: prompt,
      });

      // Stream progress updates until API completes
      let currentStepIndex = 0;
      let response: any;

      let prevProgress = 10;
      while (currentStepIndex < progressSteps.length) {
        const step = progressSteps[currentStepIndex];
        const nextProgress = step.progress;
        const interval = 100; // ms
        const steps = Math.floor(step.delay / interval);
        let elapsed = 0;
        let interrupted = false;

        for (let i = 1; i <= steps; i++) {
          // Race between interval and API completion
          const delayPromise = new Promise((resolve) =>
            setTimeout(resolve, interval)
          );
          const raceResult = await Promise.race([
            delayPromise.then(() => ({ type: "delay" as const })),
            apiPromise
              .then((result) => ({ type: "api" as const, result }))
              .catch((error) => ({ type: "error" as const, error })),
          ]);

          if (raceResult.type !== "delay") {
            if (raceResult.type === "error") {
              throw raceResult.error;
            }
            response = raceResult.result;
            console.log(`🚀 API completed early at step ${step.progress}%`);
            yield {
              progress: 98,
              status: "API completed early! Processing result...",
              isComplete: false,
            };
            interrupted = true;
            break;
          }

          // Calculate smooth progress
          const smoothProgress =
            prevProgress + ((nextProgress - prevProgress) * i) / steps;
          // Only show partial preview for main steps
          if (smoothProgress >= 25 && smoothProgress < 95 && i === steps) {
            const partialBlob = await this.createPartialPreview(
              imageBlob,
              nextProgress
            );
            const partialUrl = URL.createObjectURL(partialBlob);
            yield {
              partialImageUrl: partialUrl,
              partialImageBlob: partialBlob,
              progress: nextProgress,
              status: step.status,
              isComplete: false,
            };
          } else {
            yield {
              progress: smoothProgress,
              status: step.status,
              isComplete: false,
            };
          }
          elapsed += interval;
        }
        if (interrupted) break;
        prevProgress = nextProgress;
        currentStepIndex++;
      }

      // If we completed all steps without API finishing, wait for it
      if (!response) {
        console.log("⏳ All progress steps completed, waiting for API...");
        response = await apiPromise;
      }

      if (!response.data || response.data.length === 0) {
        throw new Error("No edited image returned from OpenAI");
      }

      const editedImageData = response.data[0];
      if (!editedImageData.b64_json) {
        throw new Error("No base64 image data in response");
      }

      // Convert base64 to blob
      const editedImageBlob = await this.base64ToBlob(
        editedImageData.b64_json,
        "image/png"
      );

      // Create object URL for preview
      const editedImageUrl = URL.createObjectURL(editedImageBlob);

      const processingTime = Date.now() - startTime;

      console.log("✅ Streaming image editing completed successfully:", {
        prompt,
        processingTime: `${processingTime}ms`,
        imageSize: `${editedImageBlob.size} bytes`,
      });

      const finalResult: EditResult = {
        editedImageUrl,
        editedImageBlob,
        prompt,
        processingTime,
        success: true,
      };

      // Final yield with complete result
      yield {
        progress: 100,
        status: "Image editing complete!",
        isComplete: true,
        finalResult,
      };

      return finalResult;
    } catch (error) {
      const processingTime = Date.now() - startTime;
      console.error("❌ Streaming image editing failed:", error);

      const errorResult: EditResult = {
        editedImageUrl: "",
        editedImageBlob: new Blob(),
        prompt,
        processingTime,
        success: false,
        error:
          error instanceof Error ? error.message : "Unknown error occurred",
      };

      yield {
        progress: 100,
        status: "Image editing failed",
        isComplete: true,
        finalResult: errorResult,
      };

      return errorResult;
    }
  }

  // Legacy method for backward compatibility
  async editSingleImage(
    imageBlob: Blob,
    prompt: string,
    onProgress?: (progress: number, status: string) => void
  ): Promise<EditResult> {
    let finalResult: EditResult | null = null;

    for await (const update of this.editSingleImageStream(imageBlob, prompt)) {
      onProgress?.(update.progress, update.status);

      if (update.isComplete && update.finalResult) {
        finalResult = update.finalResult;
        break;
      }
    }

    return (
      finalResult || {
        editedImageUrl: "",
        editedImageBlob: new Blob(),
        prompt,
        processingTime: 0,
        success: false,
        error: "Streaming failed to complete",
      }
    );
  }

  private async createPartialPreview(
    originalBlob: Blob,
    progress: number
  ): Promise<Blob> {
    return new Promise((resolve) => {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      const img = new Image();

      img.onload = () => {
        canvas.width = img.width;
        canvas.height = img.height;

        if (ctx) {
          // Draw the original image
          ctx.drawImage(img, 0, 0);

          // Apply progressive effects based on progress
          const intensity = progress / 100;

          // Add a subtle filter effect to simulate processing
          ctx.globalCompositeOperation = "overlay";
          ctx.fillStyle = `rgba(255, 165, 0, ${0.1 * intensity})`; // Orange tint
          ctx.fillRect(0, 0, canvas.width, canvas.height);

          // Add some blur effect for early stages
          if (progress < 50) {
            ctx.filter = `blur(${(50 - progress) / 10}px)`;
            ctx.drawImage(canvas, 0, 0);
            ctx.filter = "none";
          }
        }

        canvas.toBlob((blob) => {
          resolve(blob || originalBlob);
        }, "image/png");
      };

      img.src = URL.createObjectURL(originalBlob);
    });
  }

  async editMultipleImages(
    imageBlobs: Blob[],
    prompt: string,
    onProgress?: (imageIndex: number, progress: number, status: string) => void
  ): Promise<EditResult[]> {
    const results: EditResult[] = [];

    for (let i = 0; i < imageBlobs.length; i++) {
      onProgress?.(i, 0, `Starting edit for image ${i + 1}...`);

      const result = await this.editSingleImage(
        imageBlobs[i],
        prompt,
        (progress, status) => onProgress?.(i, progress, status)
      );

      results.push(result);
      onProgress?.(i, 100, `Completed image ${i + 1}`);
    }

    return results;
  }

  private async base64ToBlob(base64: string, mimeType: string): Promise<Blob> {
    const byteCharacters = atob(base64);
    const byteNumbers = new Array(byteCharacters.length);

    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }

    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: mimeType });
  }

  // Utility method to download edited image
  downloadImage(blob: Blob, filename: string = "edited-image.png"): void {
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  // Generate suggested edit prompts for dating profile optimization
  getSuggestedPrompts(): string[] {
    return [
      "Make the lighting more flattering and professional",
      "Enhance the background to be more appealing",
      "Improve the overall composition and framing",
      "Add a subtle warm filter to make the photo more attractive",
      "Make the colors more vibrant and eye-catching",
      "Improve the image sharpness and clarity",
      "Add a subtle bokeh effect to the background",
      "Make the photo look more professional and polished",
    ];
  }

  // Test method to simulate fast API completion (for testing early completion)
  async *editSingleImageStreamFast(
    imageBlob: Blob,
    prompt: string
  ): AsyncGenerator<StreamingEditResult, EditResult, unknown> {
    const startTime = Date.now();

    try {
      console.log("🚀 Testing fast streaming completion");

      yield { progress: 5, status: "Starting fast test...", isComplete: false };

      // Simulate very fast API completion (1 second)
      const fastApiPromise = new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            data: [
              {
                b64_json:
                  "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
              },
            ],
          });
        }, 1000);
      });

      // Normal progress steps (should be interrupted)
      const progressSteps = [
        { progress: 15, status: "This should be interrupted...", delay: 3000 },
        { progress: 25, status: "This should not appear", delay: 3000 },
        { progress: 35, status: "This should not appear", delay: 3000 },
      ];

      let currentStepIndex = 0;
      let response: any;

      while (currentStepIndex < progressSteps.length) {
        const step = progressSteps[currentStepIndex];

        const delayPromise = new Promise((resolve) =>
          setTimeout(resolve, step.delay)
        );

        const raceResult = await Promise.race([
          delayPromise.then(() => ({ type: "delay" as const })),
          fastApiPromise
            .then((result) => ({ type: "api" as const, result }))
            .catch((error) => ({ type: "error" as const, error })),
        ]);

        if (raceResult.type !== "delay") {
          if (raceResult.type === "error") {
            throw raceResult.error;
          }
          response = raceResult.result;
          console.log(`✅ Fast API completed early at step ${step.progress}%`);

          yield {
            progress: 98,
            status: "Fast API completed early! 🚀",
            isComplete: false,
          };
          break;
        }

        yield {
          progress: step.progress,
          status: step.status,
          isComplete: false,
        };
        currentStepIndex++;
      }

      if (!response) {
        response = await fastApiPromise;
      }

      // Create a simple 1x1 pixel image for testing
      const editedImageBlob = new Blob([new Uint8Array([137, 80, 78, 71])], {
        type: "image/png",
      });
      const editedImageUrl = URL.createObjectURL(editedImageBlob);
      const processingTime = Date.now() - startTime;

      const finalResult: EditResult = {
        editedImageUrl,
        editedImageBlob,
        prompt,
        processingTime,
        success: true,
      };

      yield {
        progress: 100,
        status: `Fast test completed in ${processingTime}ms!`,
        isComplete: true,
        finalResult,
      };

      return finalResult;
    } catch (error) {
      const processingTime = Date.now() - startTime;
      const errorResult: EditResult = {
        editedImageUrl: "",
        editedImageBlob: new Blob(),
        prompt,
        processingTime,
        success: false,
        error: error instanceof Error ? error.message : "Fast test failed",
      };

      yield {
        progress: 100,
        status: "Fast test failed",
        isComplete: true,
        finalResult: errorResult,
      };

      return errorResult;
    }
  }
}

// Global instance
export const imageEditingService = new ImageEditingService();
