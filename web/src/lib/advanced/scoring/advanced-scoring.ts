// Advanced scoring algorithms and methodologies

import type { ExpertAnalysis, DetailedScoring, ComparativeData } from "../types/advanced-analysis";

export interface ScoringWeights {
  photography: number;
  psychology: number;
  fashion: number;
  data_science: number;
  dating_coach: number;
}

export interface CategoryWeights {
  technical_quality: number;
  attractiveness: number;
  style_presentation: number;
  market_appeal: number;
  authenticity: number;
}

// Default scoring weights for different analysis types
export const DEFAULT_IMAGE_WEIGHTS: ScoringWeights = {
  photography: 0.25,
  psychology: 0.25,
  fashion: 0.2,
  data_science: 0.15,
  dating_coach: 0.15,
};

export const DEFAULT_BIO_WEIGHTS: ScoringWeights = {
  psychology: 0.3,
  dating_coach: 0.25,
  data_science: 0.2,
  photography: 0.1, // For visual appeal of text presentation
  fashion: 0.15, // For lifestyle and personality style
};

export const IMAGE_CATEGORY_WEIGHTS: CategoryWeights = {
  technical_quality: 0.2,
  attractiveness: 0.3,
  style_presentation: 0.25,
  market_appeal: 0.15,
  authenticity: 0.1,
};

export const BIO_CATEGORY_WEIGHTS: CategoryWeights = {
  technical_quality: 0.15, // Writing quality
  attractiveness: 0.25, // Personality appeal
  style_presentation: 0.2, // Interest presentation
  market_appeal: 0.25, // Dating intent and engagement
  authenticity: 0.15,
};

export class AdvancedScoringEngine {
  /**
   * Calculate weighted overall score from expert analyses
   */
  calculateOverallScore(
    expertAnalyses: ExpertAnalysis[],
    weights: ScoringWeights = DEFAULT_IMAGE_WEIGHTS
  ): number {
    let totalScore = 0;
    let totalWeight = 0;

    for (const analysis of expertAnalyses) {
      const weight = weights[analysis.expertType] || 0;
      totalScore += analysis.score * weight;
      totalWeight += weight;
    }

    return totalWeight > 0 ? Math.round(totalScore / totalWeight) : 0;
  }

  /**
   * Calculate detailed scoring breakdown with sub-components
   */
  calculateDetailedScoring(
    expertAnalyses: ExpertAnalysis[],
    categoryWeights: CategoryWeights,
    scoringWeights: ScoringWeights
  ): DetailedScoring {
    const subScores: DetailedScoring["subScores"] = {};

    // Group expert analyses by category
    const categorizedScores = this.categorizeExpertScores(expertAnalyses);

    // Calculate sub-scores for each category
    for (const [category, scores] of Object.entries(categorizedScores)) {
      const categoryWeight = categoryWeights[category as keyof CategoryWeights] || 0;
      const avgScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;

      subScores[category] = {
        score: Math.round(avgScore),
        weight: categoryWeight,
        components: this.getComponentScores(category, expertAnalyses),
      };
    }

    const overallScore = this.calculateOverallScore(expertAnalyses, scoringWeights);

    return {
      overallScore,
      subScores,
      percentileRank: this.calculatePercentileRank(overallScore),
      improvementPotential: this.calculateImprovementPotential(subScores),
      marketCompetitiveness: this.calculateMarketCompetitiveness(overallScore, subScores),
    };
  }

  /**
   * Categorize expert scores into main categories
   */
  private categorizeExpertScores(expertAnalyses: ExpertAnalysis[]): Record<string, number[]> {
    const categories: Record<string, number[]> = {
      technical_quality: [],
      attractiveness: [],
      style_presentation: [],
      market_appeal: [],
      authenticity: [],
    };

    for (const analysis of expertAnalyses) {
      switch (analysis.expertType) {
        case "photography":
          categories.technical_quality.push(analysis.score);
          break;
        case "psychology":
          categories.attractiveness.push(analysis.score);
          categories.authenticity.push(analysis.score);
          break;
        case "fashion":
          categories.style_presentation.push(analysis.score);
          break;
        case "data_science":
          categories.market_appeal.push(analysis.score);
          break;
        case "dating_coach":
          categories.market_appeal.push(analysis.score);
          categories.authenticity.push(analysis.score);
          break;
      }
    }

    return categories;
  }

  /**
   * Get component scores for a specific category
   */
  private getComponentScores(
    category: string,
    expertAnalyses: ExpertAnalysis[]
  ): Record<string, number> {
    const components: Record<string, number> = {};

    // This would be expanded based on specific analysis components
    // For now, using expert scores as components
    for (const analysis of expertAnalyses) {
      if (this.isExpertRelevantToCategory(analysis.expertType, category)) {
        components[analysis.expertType] = analysis.score;
      }
    }

    return components;
  }

  /**
   * Check if expert type is relevant to category
   */
  private isExpertRelevantToCategory(expertType: string, category: string): boolean {
    const relevanceMap: Record<string, string[]> = {
      technical_quality: ["photography"],
      attractiveness: ["psychology", "photography"],
      style_presentation: ["fashion", "photography"],
      market_appeal: ["data_science", "dating_coach"],
      authenticity: ["psychology", "dating_coach"],
    };

    return relevanceMap[category]?.includes(expertType) || false;
  }

  /**
   * Calculate percentile rank based on score
   */
  calculatePercentileRank(score: number): number {
    // Simplified percentile calculation
    // In production, this would use actual data distribution
    if (score >= 90) return 95;
    if (score >= 80) return 85;
    if (score >= 70) return 70;
    if (score >= 60) return 55;
    if (score >= 50) return 40;
    if (score >= 40) return 25;
    if (score >= 30) return 15;
    return 5;
  }

  /**
   * Calculate improvement potential based on sub-scores
   */
  calculateImprovementPotential(subScores: DetailedScoring["subScores"]): number {
    const scores = Object.values(subScores).map((s) => s.score);
    const maxScore = Math.max(...scores);
    const minScore = Math.min(...scores);
    const avgScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;

    // Higher potential if there's significant variance and room for improvement
    const variance = maxScore - minScore;
    const roomForImprovement = 100 - avgScore;

    return Math.min(100, Math.round(variance * 0.3 + roomForImprovement * 0.7));
  }

  /**
   * Calculate market competitiveness score
   */
  calculateMarketCompetitiveness(
    overallScore: number,
    subScores: DetailedScoring["subScores"]
  ): number {
    // Market competitiveness considers both overall score and balance across categories
    const categoryScores = Object.values(subScores).map((s) => s.score);
    const balance = this.calculateBalance(categoryScores);

    // Weighted combination of overall score and balance
    return Math.round(overallScore * 0.7 + balance * 0.3);
  }

  /**
   * Calculate balance score (how evenly distributed the scores are)
   */
  private calculateBalance(scores: number[]): number {
    if (scores.length === 0) return 0;

    const mean = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    const variance =
      scores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / scores.length;
    const standardDeviation = Math.sqrt(variance);

    // Lower standard deviation = better balance = higher score
    // Normalize to 0-100 scale
    return Math.max(0, Math.round(100 - standardDeviation * 2));
  }

  /**
   * Generate comparative analysis data
   */
  generateComparativeAnalysis(
    overallScore: number,
    detailedScoring: DetailedScoring,
    expertAnalyses: ExpertAnalysis[]
  ): ComparativeData {
    const percentileRank = detailedScoring.percentileRank;

    // Determine market position
    let marketPosition: ComparativeData["marketPosition"];
    if (percentileRank >= 90) marketPosition = "top_tier";
    else if (percentileRank >= 70) marketPosition = "above_average";
    else if (percentileRank >= 40) marketPosition = "average";
    else if (percentileRank >= 20) marketPosition = "below_average";
    else marketPosition = "needs_work";

    // Extract competitive advantages and areas for improvement
    const competitiveAdvantages = this.extractCompetitiveAdvantages(
      detailedScoring,
      expertAnalyses
    );
    const areasForImprovement = this.extractAreasForImprovement(detailedScoring, expertAnalyses);

    return {
      percentileRank,
      topPercentile: this.calculateTopPercentile(percentileRank),
      averageScore: 65, // This would come from actual data
      competitiveAdvantages,
      areasForImprovement,
      marketPosition,
    };
  }

  private calculateTopPercentile(percentileRank: number): number {
    return Math.max(1, 100 - percentileRank);
  }

  private extractCompetitiveAdvantages(
    detailedScoring: DetailedScoring,
    expertAnalyses: ExpertAnalysis[]
  ): string[] {
    const advantages: string[] = [];

    // Find categories with high scores
    for (const [category, data] of Object.entries(detailedScoring.subScores)) {
      if (data.score >= 80) {
        advantages.push(`Strong ${category.replace("_", " ")}`);
      }
    }

    // Extract from expert analyses
    for (const analysis of expertAnalyses) {
      if (analysis.score >= 80) {
        advantages.push(...analysis.keyObservations.slice(0, 1));
      }
    }

    return advantages.slice(0, 5); // Limit to top 5
  }

  private extractAreasForImprovement(
    detailedScoring: DetailedScoring,
    expertAnalyses: ExpertAnalysis[]
  ): string[] {
    const improvements: string[] = [];

    // Find categories with low scores
    for (const [category, data] of Object.entries(detailedScoring.subScores)) {
      if (data.score < 60) {
        improvements.push(`Improve ${category.replace("_", " ")}`);
      }
    }

    // Extract from expert analyses
    for (const analysis of expertAnalyses) {
      if (analysis.score < 70) {
        improvements.push(...analysis.recommendations.slice(0, 1).map((r) => r.recommendation));
      }
    }

    return improvements.slice(0, 5); // Limit to top 5
  }
}
