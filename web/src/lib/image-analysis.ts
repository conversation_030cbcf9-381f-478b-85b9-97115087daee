/// <reference types="vite/client" />

import { openrouter } from "@openrouter/ai-sdk-provider";
import { generateText } from "ai";
import { ANALYSIS_STEPS, type StepResult } from "@/types/analysis";

export class ImageAnalysisAgent {
  private apiKey: string;

  constructor() {
    // Get API key from environment variables
    this.apiKey = import.meta.env.VITE_OPENROUTER_API_KEY;

    if (!this.apiKey) {
      console.error("🔑 VITE_OPENROUTER_API_KEY not found in environment variables");
      throw new Error(
        "OpenRouter API key is required. Please set VITE_OPENROUTER_API_KEY in your .env file"
      );
    }

    console.log("🔑 OpenRouter API key loaded successfully");
    console.log(`🔑 API Key preview: ${this.apiKey.substring(0, 10)}...`);

    // Set the API key for OpenRouter provider
    // The provider expects OPENROUTER_API_KEY environment variable
    if (typeof globalThis !== "undefined") {
      (globalThis as any).process = (globalThis as any).process || {};
      (globalThis as any).process.env = (globalThis as any).process.env || {};
      (globalThis as any).process.env.OPENROUTER_API_KEY = this.apiKey;
    }
  }

  async analyzeImage(
    imageBase64: string,
    _fileName: string,
    onProgress?: (step: number, stepName: string, progress: number) => void
  ): Promise<StepResult[]> {
    const results: StepResult[] = [];

    for (let i = 0; i < ANALYSIS_STEPS.length; i++) {
      const step = ANALYSIS_STEPS[i];
      onProgress?.(step.id, step.name, (i / ANALYSIS_STEPS.length) * 100);

      const startTime = Date.now();

      try {
        console.log(`🔍 Starting analysis step ${step.id}: ${step.name}`);
        const result = await this.executeAnalysisStep(step.id, step.name, imageBase64);
        const processingTime = Date.now() - startTime;

        console.log(`📊 Image Analysis Step ${step.id} (${step.name}):`, {
          score: result.score,
          insights: result.insights,
          confidence: result.confidence,
          processingTime: `${processingTime}ms`,
        });

        results.push({
          stepId: step.id,
          stepName: step.name,
          score: result.score,
          insights: result.insights,
          confidence: result.confidence,
          processingTime,
        });

        onProgress?.(step.id, step.name, ((i + 1) / ANALYSIS_STEPS.length) * 100);
      } catch (error) {
        console.error(`❌ Error in step ${step.id} (${step.name}):`, error);
        results.push({
          stepId: step.id,
          stepName: step.name,
          score: 0,
          insights: ["Analysis failed for this step. Please try again."],
          confidence: 0,
          processingTime: Date.now() - startTime,
        });
      }
    }

    // Log final summary
    const avgScore = results.reduce((sum, r) => sum + r.score, 0) / results.length;
    console.log(`📊 FINAL IMAGE ANALYSIS RESULTS:`, {
      averageScore: avgScore.toFixed(1),
      stepScores: results.map((r) => ({ step: r.stepName, score: r.score })),
      totalSteps: results.length,
    });

    return results;
  }

  private async executeAnalysisStep(
    stepId: number,
    _stepName: string,
    imageBase64: string
  ): Promise<{ score: number; insights: string[]; confidence: number }> {
    const prompt = this.getStepPrompt(stepId);

    console.log(`🤖 Calling OpenRouter API for step ${stepId} with model: google/gemini-2.5-flash`);

    // Create model instance
    const model = openrouter("google/gemini-2.5-flash");

    const { text } = await generateText({
      model,
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: prompt,
            },
            {
              type: "image",
              image: `data:image/jpeg;base64,${imageBase64}`,
            },
          ],
        },
      ],
      maxTokens: 1000,
      temperature: 0.3,
    });

    return this.parseAnalysisResult(text);
  }

  private getStepPrompt(stepId: number): string {
    const baseInstruction = `You are a BRUTALLY HONEST dating profile photo expert. Most photos are mediocre and deserve low scores. Be ruthlessly critical and objective.

SCORING PHILOSOPHY:
- 90-100: EXCEPTIONAL - Top 5% of all dating photos (near-perfect)
- 80-89: EXCELLENT - Top 15% (very strong with minor flaws)
- 70-79: GOOD - Above average but notable issues
- 60-69: AVERAGE - Typical photo, significant improvement needed
- 50-59: BELOW AVERAGE - Multiple issues, major work needed
- 40-49: POOR - Serious problems, likely to perform badly
- 30-39: VERY POOR - Major red flags, repels matches
- 20-29: TERRIBLE - Fundamentally broken
- 10-19: AWFUL - Actively harmful to dating prospects
- 0-9: CATASTROPHIC - Should not be used

Provide a JSON response with exactly this structure:
{
  "score": <number 0-100>,
  "insights": ["harsh_truth1", "critical_flaw2", "brutal_feedback3"],
  "confidence": <number 0-100>
}

Be BRUTALLY HONEST. Most photos deserve 30-60 scores. Only exceptional photos get 80+.`;

    const stepPrompts = {
      1: `${baseInstruction}

STEP 1: TECHNICAL QUALITY ASSESSMENT - BE RUTHLESSLY CRITICAL
Most photos have terrible technical quality. Be harsh about every flaw:

CRITICAL ASSESSMENT AREAS:
- Image resolution and sharpness (most are blurry/pixelated)
- Lighting quality (harsh shadows, poor exposure, unflattering angles)
- Composition and framing (off-center, poor cropping, amateur mistakes)
- Background quality (cluttered, distracting, unprofessional)
- Color balance and saturation (oversaturated, poor white balance)
- Overall photo clarity (most are amateur selfies)

HARSH REALITY: Most dating photos are low-quality selfies that hurt rather than help. Be brutal about technical flaws.`,

      2: `${baseInstruction}

STEP 2: FACIAL ANALYSIS & ATTRACTIVENESS - BE BRUTALLY HONEST
Most people have poor facial presentation in photos. Be ruthlessly critical:

HARSH ASSESSMENT AREAS:
- Facial symmetry and proportions (identify unflattering angles)
- Eye contact and gaze direction (weak/avoidant vs. confident)
- Smile authenticity (forced/fake vs. genuine - most are fake)
- Facial grooming (poor beard maintenance, unkempt eyebrows, skin issues)
- Facial angle and positioning (unflattering angles, double chins)
- Expression and mood (insecure, try-hard, or genuinely confident)
- Jawline definition and facial structure (how to improve presentation)
- Hair styling (outdated, unkempt, or actually flattering)
- Makeup application (overdone, poorly applied, or enhancing)

BRUTAL TRUTH: Most people choose unflattering angles and expressions. Be harsh about what's not working.`,

      3: `${baseInstruction}

STEP 3: PHYSICAL ANALYSIS & BODY LANGUAGE - BE RUTHLESSLY CRITICAL
Most people have poor body language and physical presentation. Be brutal:

POSTURE & STANCE FLAWS:
- Body posture (slouched, insecure, tense vs. actually confident)
- Shoulder positioning (hunched, uneven, weak vs. strong)
- Spine alignment (poor posture that signals insecurity)
- Weight distribution (awkward, unbalanced positioning)

PHYSIQUE & FITNESS REALITY CHECK:
- Body proportions and how they're presented (unflattering vs. optimized)
- Fitness level visibility (out of shape, poor muscle tone, or actually fit)
- Body composition (how weight/fitness affects attractiveness)
- Physical conditioning (does this person look healthy and active?)
- Overall body shape (how well is it presented in the photo?)

BODY LANGUAGE FAILURES:
- Hand positioning (awkward, unnatural, or confident)
- Arm placement (defensive, uncomfortable, or natural)
- Body language confidence (insecure posturing vs. genuine confidence)
- Physical presence (weak energy vs. magnetic presence)
- Energy and vitality (tired, low-energy vs. vibrant)
- Approachability (intimidating, closed-off, or welcoming)

PRESENTATION DISASTERS:
- Height perception (making themselves look shorter/weaker)
- Body orientation (unflattering angles that hurt their appearance)
- Use of space (cramped, awkward positioning)
- Physical comfort (clearly uncomfortable vs. natural)
- Overall physical attractiveness (harsh reality about appeal level)

BRUTAL TRUTH: Most people's body language screams insecurity. Be harsh about what's not working.`,

      4: `${baseInstruction}

STEP 4: STYLE & PRESENTATION ANALYSIS - BE BRUTALLY CRITICAL
Most people have terrible style and presentation. Be ruthlessly honest:

STYLE DISASTERS:
- Outfit appropriateness (completely wrong for dating, outdated, juvenile)
- Color coordination (clashing colors, poor choices for skin tone)
- Clothing fit (too tight, too loose, unflattering cuts)
- Grooming and personal care (unkempt, poor hygiene indicators)
- Setting and environment (inappropriate, messy, unprofessional)
- Accessory choices (outdated, cheap-looking, or actually enhancing)
- Overall fashion sense (completely lacking vs. actually stylish)
- Color choices (making them look washed out vs. vibrant)
- Seasonal appropriateness (wearing wrong clothes for weather/season)
- Brand perception (looking cheap, trying too hard, or genuinely classy)

HARSH REALITY: Most people's style choices actively hurt their attractiveness. Be brutal about fashion failures.`,

      5: `${baseInstruction}

STEP 5: DATING PROFILE OPTIMIZATION - HARSH MARKET REALITY
Most photos fail miserably on dating apps. Be brutally honest about performance:

DATING APP FAILURES:
- Photo type suitability (completely wrong for main profile, generic filler)
- Uniqueness and memorability (boring, forgettable, blends into crowd)
- Conversation starter potential (gives nothing to talk about)
- Age-appropriate presentation (trying too hard to look younger/older)
- Authenticity and genuineness (fake, try-hard, or actually authentic)
- Appeal to target demographic (completely missing the mark)
- Competition analysis (how this stacks against other profiles)
- Swipe-worthiness (would people actually swipe right?)

BRUTAL TRUTH: Most photos get ignored or swiped left immediately. Be harsh about dating market reality.`,

      6: `${baseInstruction}

STEP 6: OVERALL RECOMMENDATIONS & BRUTAL ACTION PLAN
Provide harsh but necessary recommendations based on critical analysis:

CRITICAL IMPROVEMENT PRIORITIES:
- Priority improvement areas (what's failing most badly)
- Specific actionable advice (exactly what needs to change)
- Photo ranking suggestions (where this fits in profile hierarchy)
- Most impactful changes (what would help most)
- Overall dating profile strategy (realistic expectations)
- Timeline for improvements (how long real change takes)
- Harsh reality check (whether this photo should be used at all)

BRUTAL TRUTH: Most photos need major work or complete replacement. Be honest about what's salvageable.`,
    };

    return stepPrompts[stepId as keyof typeof stepPrompts] || stepPrompts[1];
  }

  private parseAnalysisResult(response: string): {
    score: number;
    insights: string[];
    confidence: number;
  } {
    try {
      // Extract JSON from response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error("No JSON found in response");
      }

      const parsed = JSON.parse(jsonMatch[0]);

      return {
        score: Math.max(0, Math.min(100, parseInt(parsed.score) || 0)),
        insights: Array.isArray(parsed.insights)
          ? parsed.insights.slice(0, 4).map(String)
          : ["Unable to generate insights for this step"],
        confidence: Math.max(0, Math.min(100, parseInt(parsed.confidence) || 0)),
      };
    } catch (error) {
      console.error("🔧 Failed to parse analysis result:", error);
      console.log("📝 Raw response:", response);
      return {
        score: 50,
        insights: [
          "Analysis completed but results could not be parsed properly",
          "Please try again or check the image quality",
        ],
        confidence: 30,
      };
    }
  }

  calculateOverallScore(results: StepResult[]): number {
    if (results.length === 0) return 0;

    // Weighted scoring - some steps matter more for dating profiles
    const weights = {
      1: 0.12, // Technical Quality
      2: 0.28, // Facial Analysis (most important)
      3: 0.22, // Physical Analysis (very important)
      4: 0.18, // Style & Presentation
      5: 0.18, // Dating Optimization
      6: 0.02, // Final Recommendations
    };

    let weightedSum = 0;
    let totalWeight = 0;

    results.forEach((result) => {
      const weight = weights[result.stepId as keyof typeof weights] || 0.2;
      weightedSum += result.score * weight;
      totalWeight += weight;
    });

    return Math.round(weightedSum / totalWeight);
  }

  generateFinalRecommendations(results: StepResult[]): string[] {
    const recommendations: string[] = [];

    // Analyze scores to prioritize recommendations
    const lowScoreSteps = results.filter((r) => r.score < 60).sort((a, b) => a.score - b.score);
    const mediumScoreSteps = results.filter((r) => r.score >= 60 && r.score < 80);

    if (lowScoreSteps.length > 0) {
      recommendations.push(
        `Priority: Improve ${lowScoreSteps[0].stepName.toLowerCase()} (scored ${lowScoreSteps[0].score}/100)`
      );
    }

    if (mediumScoreSteps.length > 0) {
      recommendations.push(
        `Secondary: Enhance ${mediumScoreSteps[0].stepName.toLowerCase()} for better results`
      );
    }

    const highestStep = results.reduce((max, r) => (r.score > max.score ? r : max), results[0]);
    if (highestStep.score > 80) {
      recommendations.push(
        `Strength: Your ${highestStep.stepName.toLowerCase()} is excellent - use this photo type more`
      );
    }

    // Add general recommendations based on overall score
    const overallScore = this.calculateOverallScore(results);
    if (overallScore < 50) {
      recommendations.push("Consider retaking this photo with better preparation and setup");
    } else if (overallScore < 70) {
      recommendations.push("This photo has potential - focus on the priority improvements above");
    } else {
      recommendations.push("Great photo! Minor tweaks could make it even better");
    }

    return recommendations;
  }
}

export const imageAnalysisAgent = new ImageAnalysisAgent();
