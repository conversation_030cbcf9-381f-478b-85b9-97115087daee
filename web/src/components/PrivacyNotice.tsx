import { Database, Info, Shield, Trash2 } from "lucide-react";
import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { usePrivacyControls } from "./PrivacyManager";

export function PrivacyNotice() {
  const [isOpen, setIsOpen] = useState(false);
  const [dataUsage, setDataUsage] = useState<{ totalImages: number; sizeMB: string } | null>(null);
  const { clearAllData, getDataUsage } = usePrivacyControls();

  const handleGetUsage = async () => {
    const usage = await getDataUsage();
    setDataUsage(usage);
  };

  const handleClearData = async () => {
    const success = await clearAllData();
    if (success) {
      setDataUsage({ totalImages: 0, sizeMB: "0.00" });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="text-graphite-60 hover:text-graphite-90"
          onClick={handleGetUsage}
        >
          <Shield className="h-4 w-4 mr-1" />
          Privacy
        </Button>
      </DialogTrigger>

      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-success-green" />
            Privacy & Data Protection
          </DialogTitle>
          <DialogDescription>
            Your privacy is our priority. Here's how we handle your images.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Privacy Features */}
          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <Database className="h-5 w-5 text-blue-500 mt-0.5" />
              <div>
                <h4 className="font-medium text-sm">Local Storage Only</h4>
                <p className="text-xs text-graphite-60">
                  Images are stored temporarily in your browser's IndexedDB, never on our servers
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <Shield className="h-5 w-5 text-success-green mt-0.5" />
              <div>
                <h4 className="font-medium text-sm">No Server Upload</h4>
                <p className="text-xs text-graphite-60">
                  AI analysis happens client-side, images never leave your device
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <Trash2 className="h-5 w-5 text-orange-500 mt-0.5" />
              <div>
                <h4 className="font-medium text-sm">Auto-Cleanup</h4>
                <p className="text-xs text-graphite-60">
                  Images are automatically deleted after 24 hours or when you close the tab
                </p>
              </div>
            </div>
          </div>

          {/* Current Usage */}
          {dataUsage && (
            <div className="border-t pt-4">
              <h4 className="font-medium text-sm mb-2">Current Usage</h4>
              <div className="flex gap-2">
                <Badge variant="outline">{dataUsage.totalImages} images</Badge>
                <Badge variant="outline">{dataUsage.sizeMB} MB</Badge>
              </div>
            </div>
          )}

          {/* Manual Clear */}
          <div className="border-t pt-4 space-y-2">
            <h4 className="font-medium text-sm">Manual Controls</h4>
            <Button variant="secondary" size="sm" onClick={handleClearData} className="w-full">
              <Trash2 className="h-4 w-4 mr-2" />
              Clear All Data Now
            </Button>
            <p className="text-xs text-graphite-60">
              This will remove all images and analysis results from your browser
            </p>
          </div>

          {/* Information */}
          <div className="bg-blue-50 p-3 rounded-md">
            <div className="flex gap-2">
              <Info className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
              <p className="text-xs text-blue-700">
                For maximum privacy, use incognito/private browsing mode. All data will be
                automatically deleted when you close the window.
              </p>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
