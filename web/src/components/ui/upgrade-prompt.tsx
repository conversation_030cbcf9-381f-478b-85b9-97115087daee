import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { AlertTriangle } from "lucide-react";

interface UpgradePromptProps {
  currentScore: number;
  premiumScore: number;
  className?: string;
  onUpgrade?: () => void;
}

export function UpgradePrompt({
  currentScore,
  premiumScore,
  className,
  onUpgrade,
}: UpgradePromptProps) {
  return (
    <div className={cn("text-center space-y-3 sm:space-y-4", className)}>
      {/* Warning message */}
      <div className="flex items-center justify-center gap-2 px-3 sm:px-4 py-2 bg-red-50 border border-red-200 rounded-lg">
        <AlertTriangle className="h-3 w-3 sm:h-4 sm:w-4 text-red-500 flex-shrink-0" />
        <span className="text-xs sm:text-sm text-red-700 font-medium text-center">
          Low Score - Upgrade to Pro for expert insights!
        </span>
      </div>

      {/* Premium score preview */}
      <p className="text-xs sm:text-sm text-slate-600">
        This could be your score with Premium:{" "}
        <span className="font-bold text-slate-900">{premiumScore}%</span>
      </p>

      {/* Upgrade button */}
      <Button
        onClick={onUpgrade}
        size="sm"
        className="bg-gradient-to-r from-flame-red to-sparks-pink hover:opacity-90 text-white font-semibold px-6 sm:px-8 py-2 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 text-xs sm:text-sm h-8 sm:h-10"
      >
        Upgrade to Premium
      </Button>
    </div>
  );
}