import { cn } from "@/lib/utils";

interface AnalysisHistoryItemProps {
  service: string;
  date: string;
  score: number;
  onViewDetails?: () => void;
  className?: string;
}

export function AnalysisHistoryItem({
  service,
  date,
  score,
  onViewDetails,
  className,
}: AnalysisHistoryItemProps) {
  return (
    <div className={cn(
      "flex items-center justify-between py-3 px-1 border-b border-slate-100 last:border-b-0",
      className
    )}>
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 sm:gap-3">
          <h4 className="font-medium text-slate-900 text-xs sm:text-sm truncate">{service}</h4>
        </div>
        <p className="text-xs text-slate-500 mt-1">{date}</p>
      </div>
      
      <div className="flex items-center gap-2 sm:gap-4 flex-shrink-0">
        <div className="text-right">
          <div className="text-base sm:text-lg font-bold text-slate-900">{score}%</div>
        </div>
        <button
          onClick={onViewDetails}
          className="text-xs sm:text-sm text-flame-red hover:text-flame-red/80 font-medium transition-colors whitespace-nowrap"
        >
          <span className="hidden sm:inline">View Details</span>
          <span className="sm:hidden">Details</span>
        </button>
      </div>
    </div>
  );
}