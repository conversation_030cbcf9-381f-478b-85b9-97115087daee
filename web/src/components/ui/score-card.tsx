import { cn } from "@/lib/utils";
import { useEffect, useState } from "react";

interface ScoreCardProps {
  label: string;
  value: number;
  max?: number;
  className?: string;
  animate?: boolean;
  color?: "red" | "orange" | "green";
}

export function ScoreCard({
  label,
  value,
  max = 100,
  className,
  animate = true,
  color = "red",
}: ScoreCardProps) {
  const [animatedValue, setAnimatedValue] = useState(animate ? 0 : value);
  
  const percentage = Math.min(Math.max(value / max, 0), 1) * 100;
  const animatedPercentage = Math.min(Math.max(animatedValue / max, 0), 1) * 100;

  useEffect(() => {
    if (animate) {
      const timer = setTimeout(() => {
        setAnimatedValue(value);
      }, 200);
      return () => clearTimeout(timer);
    }
  }, [value, animate]);

  const colorClasses = {
    red: "bg-gradient-to-r from-flame-red to-sparks-pink",
    orange: "bg-gradient-to-r from-amber-500 to-orange-500", 
    green: "bg-gradient-to-r from-green-500 to-emerald-500",
  };

  return (
    <div className={cn("flex items-center gap-3 sm:gap-4", className)}>
      <div className="min-w-0 flex-1">
        <div className="flex items-center justify-between mb-1.5 sm:mb-2">
          <span className="text-xs sm:text-sm font-medium text-slate-700">{label}</span>
          <span className="text-xs sm:text-sm font-semibold text-slate-900">{Math.round(percentage)}%</span>
        </div>
        <div className="w-full bg-slate-200 rounded-full h-1.5 sm:h-2">
          <div
            className={cn(
              "h-1.5 sm:h-2 rounded-full transition-all duration-1000 ease-out",
              colorClasses[color]
            )}
            style={{
              width: `${animatedPercentage}%`,
            }}
          />
        </div>
      </div>
    </div>
  );
}