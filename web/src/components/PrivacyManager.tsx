import { useEffect } from "react";
import { imageStorage, sessionManager } from "@/lib/storage";

interface PrivacyManagerProps {
  children: React.ReactNode;
}

export function PrivacyManager({ children }: PrivacyManagerProps) {
  useEffect(() => {
    // Set up periodic cleanup
    const cleanupInterval = setInterval(
      async () => {
        try {
          await imageStorage.cleanup();
        } catch (error) {
          console.warn("Failed to cleanup old images:", error);
        }
      },
      30 * 60 * 1000
    ); // Every 30 minutes

    // Cleanup on page unload/refresh
    const handleBeforeUnload = async () => {
      try {
        const session = sessionManager.getCurrentSession();
        await imageStorage.clearSession(session.id);
        sessionManager.clearSession();
      } catch (error) {
        console.warn("Failed to cleanup on page unload:", error);
      }
    };

    // Add event listeners
    window.addEventListener("beforeunload", handleBeforeUnload);
    window.addEventListener("pagehide", handleBeforeUnload);

    // Cleanup function
    return () => {
      clearInterval(cleanupInterval);
      window.removeEventListener("beforeunload", handleBeforeUnload);
      window.removeEventListener("pagehide", handleBeforeUnload);
    };
  }, []);

  // Cleanup on visibility change (when tab becomes hidden)
  useEffect(() => {
    const handleVisibilityChange = async () => {
      if (document.hidden) {
        // Optional: cleanup when tab becomes hidden
        // This is more aggressive cleanup
        try {
          await imageStorage.cleanup();
        } catch (error) {
          console.warn("Failed to cleanup on visibility change:", error);
        }
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);
    return () => document.removeEventListener("visibilitychange", handleVisibilityChange);
  }, []);

  return <>{children}</>;
}

// Hook for manual privacy controls
export function usePrivacyControls() {
  const clearAllData = async () => {
    try {
      const session = sessionManager.getCurrentSession();
      await imageStorage.clearSession(session.id);
      sessionManager.clearSession();

      // Clear any remaining object URLs
      const objectUrls = document.querySelectorAll('img[src^="blob:"]');
      objectUrls.forEach((img) => {
        const src = (img as HTMLImageElement).src;
        if (src.startsWith("blob:")) {
          URL.revokeObjectURL(src);
        }
      });

      return true;
    } catch (error) {
      console.error("Failed to clear all data:", error);
      return false;
    }
  };

  const getDataUsage = async () => {
    try {
      const session = sessionManager.getCurrentSession();
      const images = await imageStorage.getSessionImages(session.id);

      const totalSize = images.reduce((sum, img) => sum + img.blob.size, 0);
      const totalImages = images.length;

      return {
        totalSize,
        totalImages,
        sizeMB: (totalSize / (1024 * 1024)).toFixed(2),
      };
    } catch (error) {
      console.error("Failed to get data usage:", error);
      return null;
    }
  };

  return {
    clearAllData,
    getDataUsage,
  };
}
