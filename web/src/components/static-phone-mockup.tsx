import { cn } from "@/lib/utils";

export function StaticPhoneMockup({
  rotation = 0,
  className,
}: {
  rotation?: number;
  className?: string;
}) {
  return (
    <div
      className={cn(
        "absolute h-[600px] w-[300px] rounded-[40px] bg-white shadow-2xl border-4 border-gray-200",
        className
      )}
      style={{ transform: `rotate(${rotation}deg)` }}
    >
      <div className="p-4">
        <div className="w-full h-[350px] rounded-2xl bg-gray-200" />
        <div className="mt-4 space-y-3">
          <div className="h-6 w-3/4 rounded bg-gray-200" />
          <div className="h-4 w-full rounded bg-gray-200" />
          <div className="h-4 w-5/6 rounded bg-gray-200" />
        </div>
      </div>
    </div>
  );
}
