import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import {
  imageStorage,
  sessionManager,
  type StoredImage,
  createImagePreview,
  revokeImagePreview,
} from "@/lib/storage";

export interface ImageWithPreview {
  id: string;
  fileName: string;
  preview: string;
  blob: Blob;
  mimeType: string;
  uploadedAt: number;
  sessionId: string;
  isEdited?: boolean;
  originalImageId?: string;
  editPrompt?: string;
  editedAt?: number;
  originalImageBlob?: Blob;
}

export interface ImageStoreState {
  // Original uploaded images
  originalImages: ImageWithPreview[];
  // AI enhanced/edited images
  enhancedImages: ImageWithPreview[];
  // Loading states
  isLoadingImages: boolean;
  isStorageReady: boolean;
  // Current session
  sessionId: string;
  
  // Actions
  initializeStore: () => Promise<void>;
  addOriginalImages: (files: File[]) => Promise<string[]>;
  addEnhancedImage: (
    editedBlob: Blob,
    originalImageId: string,
    editPrompt: string,
    originalImageBlob?: Blob
  ) => Promise<string>;
  removeOriginalImage: (imageId: string) => Promise<void>;
  removeEnhancedImage: (imageId: string) => Promise<void>;
  clearOriginalImages: () => Promise<void>;
  clearEnhancedImages: () => Promise<void>;
  clearAllImages: () => Promise<void>;
  getOriginalImage: (imageId: string) => ImageWithPreview | undefined;
  getEnhancedImages: (originalImageId: string) => ImageWithPreview[];
  getAllEnhancedImages: () => ImageWithPreview[];
  refreshImages: () => Promise<void>;
}

export const useImageStore = create<ImageStoreState>()(
  devtools(
    persist(
      (set, get) => ({
        originalImages: [],
        enhancedImages: [],
        isLoadingImages: false,
        isStorageReady: false,
        sessionId: "",

        initializeStore: async () => {
          set({ isLoadingImages: true });
          
          try {
            await imageStorage.init();
            const session = sessionManager.getCurrentSession();
            
            set({ sessionId: session.id, isStorageReady: true });
            
            // Load all images from storage
            await get().refreshImages();
          } catch (error) {
            console.error("Failed to initialize image store:", error);
          } finally {
            set({ isLoadingImages: false });
          }
        },

        refreshImages: async () => {
          const { sessionId } = get();
          if (!sessionId) return;

          try {
            const storedImages = await imageStorage.getSessionImages(sessionId);
            
            // Separate original and enhanced images
            const originals: ImageWithPreview[] = [];
            const enhanced: ImageWithPreview[] = [];
            
            for (const img of storedImages) {
              const imageWithPreview: ImageWithPreview = {
                ...img,
                preview: createImagePreview(img.blob),
              };
              
              if (img.isEdited) {
                enhanced.push(imageWithPreview);
              } else {
                originals.push(imageWithPreview);
              }
            }
            
            // Clean up old previews
            const { originalImages: oldOriginals, enhancedImages: oldEnhanced } = get();
            [...oldOriginals, ...oldEnhanced].forEach(img => {
              revokeImagePreview(img.preview);
            });
            
            set({ 
              originalImages: originals,
              enhancedImages: enhanced 
            });
          } catch (error) {
            console.error("Failed to refresh images:", error);
          }
        },

        addOriginalImages: async (files: File[]) => {
          const { sessionId, originalImages } = get();
          if (!sessionId) throw new Error("No session initialized");
          
          const newImageIds: string[] = [];
          const newImages: ImageWithPreview[] = [];
          
          for (const file of files) {
            try {
              const imageId = await imageStorage.storeImage(file, sessionId);
              sessionManager.addImageToSession(imageId);
              
              const imageWithPreview: ImageWithPreview = {
                id: imageId,
                fileName: file.name,
                preview: createImagePreview(file),
                blob: file,
                mimeType: file.type,
                uploadedAt: Date.now(),
                sessionId,
              };
              
              newImages.push(imageWithPreview);
              newImageIds.push(imageId);
            } catch (error) {
              console.error(`Failed to store image ${file.name}:`, error);
            }
          }
          
          set({ originalImages: [...originalImages, ...newImages] });
          return newImageIds;
        },

        addEnhancedImage: async (
          editedBlob: Blob,
          originalImageId: string,
          editPrompt: string,
          originalImageBlob?: Blob
        ) => {
          const { sessionId, enhancedImages } = get();
          if (!sessionId) throw new Error("No session initialized");
          
          try {
            const imageId = await imageStorage.storeEditedImage(
              editedBlob,
              originalImageId,
              editPrompt,
              sessionId,
              originalImageBlob
            );
            
            const imageWithPreview: ImageWithPreview = {
              id: imageId,
              fileName: `edited_${Date.now()}.png`,
              preview: createImagePreview(editedBlob),
              blob: editedBlob,
              mimeType: "image/png",
              uploadedAt: Date.now(),
              sessionId,
              isEdited: true,
              originalImageId,
              editPrompt,
              editedAt: Date.now(),
              originalImageBlob,
            };
            
            set({ enhancedImages: [...enhancedImages, imageWithPreview] });
            return imageId;
          } catch (error) {
            console.error("Failed to store enhanced image:", error);
            throw error;
          }
        },

        removeOriginalImage: async (imageId: string) => {
          const { originalImages } = get();
          const image = originalImages.find(img => img.id === imageId);
          
          if (image) {
            revokeImagePreview(image.preview);
            await imageStorage.deleteImage(imageId);
            sessionManager.removeImageFromSession(imageId);
            
            set({
              originalImages: originalImages.filter(img => img.id !== imageId)
            });
          }
        },

        removeEnhancedImage: async (imageId: string) => {
          const { enhancedImages } = get();
          const image = enhancedImages.find(img => img.id === imageId);
          
          if (image) {
            revokeImagePreview(image.preview);
            await imageStorage.deleteImage(imageId);
            
            set({
              enhancedImages: enhancedImages.filter(img => img.id !== imageId)
            });
          }
        },

        clearOriginalImages: async () => {
          const { originalImages, sessionId } = get();
          
          // Revoke all preview URLs
          originalImages.forEach(img => revokeImagePreview(img.preview));
          
          // Delete from storage
          for (const img of originalImages) {
            await imageStorage.deleteImage(img.id);
            sessionManager.removeImageFromSession(img.id);
          }
          
          set({ originalImages: [] });
        },

        clearEnhancedImages: async () => {
          const { enhancedImages } = get();
          
          // Revoke all preview URLs
          enhancedImages.forEach(img => revokeImagePreview(img.preview));
          
          // Delete from storage
          for (const img of enhancedImages) {
            await imageStorage.deleteImage(img.id);
          }
          
          set({ enhancedImages: [] });
        },

        clearAllImages: async () => {
          const { sessionId } = get();
          
          // Clear both original and enhanced
          await get().clearOriginalImages();
          await get().clearEnhancedImages();
          
          // Clear session
          if (sessionId) {
            await imageStorage.clearSession(sessionId);
            sessionManager.clearSession();
          }
        },

        getOriginalImage: (imageId: string) => {
          const { originalImages } = get();
          return originalImages.find(img => img.id === imageId);
        },

        getEnhancedImages: (originalImageId: string) => {
          const { enhancedImages } = get();
          return enhancedImages.filter(
            img => img.originalImageId === originalImageId
          );
        },

        getAllEnhancedImages: () => {
          return get().enhancedImages;
        },
      }),
      {
        name: "tinderop-image-store",
        partialize: (state) => ({
          // Only persist the session ID, not the actual images
          // Images are stored in IndexedDB
          sessionId: state.sessionId,
        }),
      }
    ),
    {
      name: "ImageStore",
    }
  )
);

// Helper hook to get image counts
export const useImageCounts = () => {
  const originalCount = useImageStore(state => state.originalImages.length);
  const enhancedCount = useImageStore(state => state.enhancedImages.length);
  
  return {
    originalCount,
    enhancedCount,
    totalCount: originalCount + enhancedCount,
  };
};

// Helper hook to get enhanced images for a specific original
export const useEnhancedImagesForOriginal = (originalImageId: string) => {
  return useImageStore(state => 
    state.enhancedImages.filter(img => img.originalImageId === originalImageId)
  );
};