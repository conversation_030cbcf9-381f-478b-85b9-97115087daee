export interface AnalysisStep {
  id: number;
  name: string;
  description: string;
  status: "pending" | "processing" | "completed" | "error";
  score?: number;
  insights?: string[];
  confidence?: number;
}

export interface StepResult {
  stepId: number;
  stepName: string;
  score: number;
  insights: string[];
  confidence: number;
  processingTime: number;
}

export interface AnalysisResult {
  fileName: string;
  preview: string;
  overallScore: number;
  steps: StepResult[];
  recommendations: string[];
  processed: boolean;
  error?: string;
}

export interface AnalysisProgress {
  fileName: string;
  currentStep: number;
  totalSteps: number;
  stepName: string;
  progress: number;
}

export interface ImageAnalysisRequest {
  fileName: string;
  imageData: string; // base64 encoded image
  mimeType: string;
}

export interface ImageAnalysisResponse {
  success: boolean;
  result?: AnalysisResult;
  error?: string;
  progress?: AnalysisProgress;
}

export interface StreamingAnalysisEvent {
  type: "progress" | "step-complete" | "analysis-complete" | "error";
  data: AnalysisProgress | StepResult | AnalysisResult | { error: string };
}

export const ANALYSIS_STEPS = [
  {
    id: 1,
    name: "Technical Quality",
    description: "Analyzing photo quality, lighting, and composition",
  },
  {
    id: 2,
    name: "Facial Analysis",
    description: "Evaluating facial features, expressions, and attractiveness",
  },
  {
    id: 3,
    name: "Physical Analysis",
    description: "Assessing body language, posture, and physical presentation",
  },
  {
    id: 4,
    name: "Style & Presentation",
    description: "Assessing clothing, setting, and overall presentation",
  },
  {
    id: 5,
    name: "Dating Profile Optimization",
    description: "Analyzing dating-specific appeal and suitability",
  },
  {
    id: 6,
    name: "Final Recommendations",
    description: "Synthesizing insights into actionable advice",
  },
] as const;

export const BIO_ANALYSIS_STEPS = [
  {
    id: 1,
    name: "Writing Quality",
    description: "Analyzing grammar, readability, and writing style",
  },
  {
    id: 2,
    name: "Personality Appeal",
    description: "Evaluating personality traits and attractiveness signals",
  },
  {
    id: 3,
    name: "Interest Analysis",
    description: "Assessing hobbies, interests, and lifestyle appeal",
  },
  {
    id: 4,
    name: "Dating Intent",
    description: "Analyzing clarity of dating goals and relationship signals",
  },
  {
    id: 5,
    name: "Engagement Factor",
    description: "Evaluating conversation starters and match appeal",
  },
] as const;

export interface BioAnalysisResult {
  originalBio: string;
  overallScore: number;
  steps: StepResult[];
  recommendations: string[];
  improvedBio?: string;
  processed: boolean;
  error?: string;
}

export interface BioAnalysisProgress {
  currentStep: number;
  totalSteps: number;
  stepName: string;
  progress: number;
}
