import { defineConfig } from "vite";
import tsConfigPaths from 'vite-tsconfig-paths'
import { tanstackStart } from '@tanstack/react-start/plugin/vite'
import viteReact from "@vitejs/plugin-react-oxc";
import path from "node:path";

export default defineConfig({
  server: {
    port: 6969,
  },
  plugins: [ 
    tsConfigPaths({
      projects: ["./tsconfig.json"],
    }),
    tanstackStart({ 
      customViteReactPlugin: true,  
      target: "cloudflare-module", }),
    viteReact()
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
});
