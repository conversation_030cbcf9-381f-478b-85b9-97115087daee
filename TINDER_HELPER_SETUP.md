# TinderOP Helper - Complete Setup Guide

The TinderOP Helper is a Chrome extension that provides AI-powered conversation suggestions for dating apps using real-time screenshot analysis.

## 🎯 Features

- **Real-time Conversation Analysis**: Screenshot-based AI analysis using Gemini 2.5 Flash
- **Multi-platform Support**: Works on Tinder, Bumble, and Hinge
- **Intelligent Suggestions**: Multiple response options with different tones (witty, sincere, flirty, thoughtful)
- **Privacy-focused**: Screenshots are analyzed and immediately deleted
- **Seamless Integration**: Floating action button overlay on dating apps

## 📋 Prerequisites

Before setting up the TinderOP Helper, ensure you have:

1. **TinderOP Web App Running**: The main application must be running
2. **OpenRouter API Key**: Required for Gemini 2.5 Flash access
3. **Chrome Browser**: Extension requires Chrome/Chromium
4. **Development Environment**: Node.js/Bun for running the web app

## 🚀 Quick Setup

### Step 1: Environment Configuration

1. **Set up your environment variables** in `/web/.env`:
```bash
# Required for TinderOP Helper
VITE_OPENROUTER_API_KEY=your_openrouter_api_key_here

# Optional: Custom API URL (defaults to localhost:5173)
VITE_API_URL=http://localhost:5173
```

2. **Get your OpenRouter API Key**:
   - Visit [OpenRouter.ai](https://openrouter.ai)
   - Sign up/login and generate an API key
   - Ensure you have credits for Gemini 2.5 Flash usage

### Step 2: Start the Web Application

```bash
# Navigate to the web directory
cd web

# Install dependencies (if not already done)
bun install

# Start the development server
bun run dev
```

The app should be running at `http://localhost:5173`

### Step 3: Install the Chrome Extension

1. **Open Chrome Extensions Page**:
   - Go to `chrome://extensions/`
   - Enable "Developer mode" (toggle in top-right)

2. **Load the Extension**:
   - Click "Load unpacked"
   - Select the `/chrome-extension/` folder from this project
   - The TinderOP Helper extension should appear in your extensions list

3. **Verify Installation**:
   - Look for the TinderOP Helper icon in your Chrome toolbar
   - Extension should show as "Enabled"

### Step 4: Configure the Extension

1. **Click the extension icon** in Chrome toolbar
2. **Verify settings**:
   - API URL: `http://localhost:5173` (or your deployment URL)
   - Enable Suggestions: ✅ (checked)
3. **Test connection**: Click "Test Connection" - should show "Connected"

## 💻 Usage Instructions

### Basic Usage

1. **Open a supported dating platform**:
   - [Tinder Web](https://tinder.com)
   - [Bumble Web](https://bumble.com)
   - [Hinge Web](https://hinge.co)

2. **Navigate to any conversation**

3. **Click the floating button** (💬) that appears on the page

4. **Wait for analysis** - the extension will:
   - Capture a screenshot of the conversation
   - Send it to the AI for analysis
   - Generate contextual reply suggestions

5. **Use the suggestions**:
   - Review the different response options
   - Click "Copy" on any suggestion you like
   - Paste into the message input and send

### Understanding Suggestions

Each suggestion comes with:
- **Text**: The actual suggested response
- **Tone**: witty, sincere, flirty, casual, or thoughtful
- **Copy Button**: One-click copying to clipboard

## 🔧 Advanced Configuration

### Production Deployment

For production use, update the environment and extension settings:

1. **Deploy the web app** to your preferred platform (Cloudflare Workers, Vercel, etc.)

2. **Update extension configuration**:
   - Click the extension icon
   - Change API URL to your production URL
   - Save settings

3. **Update CORS settings** if needed in `/web/functions/api/tinder-helper/analyze.ts`

### Custom Prompts

To customize the AI behavior, edit the prompt in:
- `/web/functions/api/tinder-helper/analyze.ts` (API version)
- `/web/src/lib/conversation-analysis.ts` (Direct AI version)

### Platform-Specific Settings

Each dating platform may require different selectors. Update these in:
- `/chrome-extension/content-script.js` (extractVisibleMessages function)

## 🛠️ Troubleshooting

### Extension Not Working

**Problem**: Floating button doesn't appear
- **Solution**: 
  - Refresh the dating app page
  - Check that you're on a supported platform
  - Verify extension is enabled in `chrome://extensions/`

**Problem**: "Connection failed" in extension popup
- **Solution**:
  - Ensure web app is running (`bun run dev`)
  - Check API URL in extension settings
  - Verify CORS headers are properly configured

### API Errors

**Problem**: "API configuration error"
- **Solution**:
  - Check that `VITE_OPENROUTER_API_KEY` is set in `.env`
  - Restart the web app after changing environment variables
  - Verify OpenRouter API key is valid

**Problem**: Analysis takes too long or fails
- **Solution**:
  - Check OpenRouter account credits
  - Verify internet connection
  - Try again - AI services can have temporary issues

### Screenshot Issues

**Problem**: AI can't analyze the conversation properly
- **Solution**:
  - Ensure the conversation is fully visible on screen
  - Try scrolling to show recent messages
  - Check that no overlays are blocking the conversation

## 📊 Performance Optimization

### For Better Analysis Results:

1. **Scroll to show recent messages** before clicking the button
2. **Ensure good contrast** between text and background
3. **Avoid overlapping windows** that might obscure the conversation
4. **Use on desktop** for better screenshot quality

### For Faster Processing:

1. **Keep the web app running** to avoid cold starts
2. **Use a stable internet connection**
3. **Close unnecessary browser tabs** to free up resources

## 🔒 Privacy & Security

### Data Handling:
- ✅ **Screenshots are processed and immediately deleted**
- ✅ **No conversation data is stored permanently**
- ✅ **All communication uses HTTPS**
- ✅ **No personal information is logged**

### User Control:
- ✅ **Manual screenshot capture only** (never automatic)
- ✅ **Full control over when analysis happens**
- ✅ **Can disable the extension at any time**

## 📱 Browser Compatibility

**Fully Supported:**
- Chrome 88+
- Chromium-based browsers (Edge, Brave, etc.)

**Not Supported:**
- Firefox (different extension API)
- Safari (different extension format)
- Mobile browsers (extension not compatible)

## 🆘 Support & Debugging

### Enable Debug Logging:

1. **Open Chrome DevTools** (F12)
2. **Check Console tab** for TinderOP Helper messages
3. **Look for errors** related to API calls or extension functionality

### Common Debug Messages:
- `🚀 TinderOP Helper initialized` - Extension loaded successfully
- `📸 Capturing screenshot` - Screenshot capture started
- `🤖 Sending request to Gemini 2.5 Flash` - AI analysis started
- `✅ Analysis completed` - Success message with timing

### Getting Help:

1. **Check the browser console** for error messages
2. **Verify all setup steps** have been completed
3. **Test with a simple conversation** first
4. **Report issues** with console logs and screenshots

## 📈 Usage Analytics

The extension tracks basic usage for debugging:
- Analysis success/failure rates
- Processing times
- Platform usage distribution

No personal or conversation data is included in analytics.

## 🔄 Updates & Maintenance

### Updating the Extension:
1. **Pull latest code** from the repository
2. **Go to chrome://extensions/**
3. **Click reload button** on TinderOP Helper
4. **Refresh any open dating app tabs**

### Updating the Web App:
1. **Pull latest code**
2. **Restart the development server** (`bun run dev`)
3. **Test connection** in extension popup

## 🎛️ Configuration Reference

### Environment Variables:
```bash
# Required
VITE_OPENROUTER_API_KEY=sk-or-v1-xxxxx

# Optional
VITE_API_URL=http://localhost:5173
VITE_DEBUG=true  # Enable debug logging
```

### Extension Settings:
- **API URL**: Web app location (default: http://localhost:5173)
- **Enable Suggestions**: Toggle the suggestion feature
- **Auto-detect Platform**: Automatically detect which dating app you're using

### API Endpoints:
- `GET /api/health` - Health check
- `POST /api/tinder-helper/analyze` - Conversation analysis

---

## 🎉 You're Ready!

With the TinderOP Helper set up, you now have AI-powered conversation assistance right in your dating apps. The system will analyze your conversations and provide contextual, intelligent suggestions to help you engage more effectively.

Remember: The suggestions are meant to inspire and assist, but authentic communication is key to building genuine connections. Use the AI suggestions as a starting point for your own personality to shine through!