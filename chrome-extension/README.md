# TinderOP Helper - Chrome Extension

AI-powered conversation assistant for dating apps. Get intelligent reply suggestions by analyzing your conversations in real-time.

## Features

- 🤖 **AI-Powered Suggestions**: Uses Gemini 2.5 Flash to analyze conversations and provide contextual reply suggestions
- 📱 **Overlay Interface**: Floating action button appears on dating app pages for easy access
- 🔒 **Privacy-Focused**: Screenshots are processed and immediately deleted - no permanent storage
- 🎯 **Multi-Platform**: Supports Tinder, Bumble, and Hinge
- ⚡ **Real-Time**: Get suggestions instantly without leaving the dating app

## Installation

### Development Installation (Manual)

1. **Download/Clone the Extension**
   - Navigate to the `chrome-extension/` directory in the TinderOP project
   
2. **Open Chrome Extensions Page**
   - Go to `chrome://extensions/` in your Chrome browser
   - Enable "Developer mode" (toggle in top-right corner)

3. **Load the Extension**
   - Click "Load unpacked"
   - Select the `chrome-extension/` folder
   - The extension should now appear in your extensions list

4. **Verify Installation**
   - Look for the TinderOP Helper icon in your Chrome toolbar
   - The extension should show as "Enabled"

### Production Installation (Future)

The extension will be available on the Chrome Web Store once it's ready for public release.

## Setup

### 1. Start the TinderOP Web App

Make sure the TinderOP web application is running:

```bash
cd web
bun run dev
```

The app should be accessible at `http://localhost:5173`

### 2. Configure the Extension

1. Click the TinderOP Helper icon in your Chrome toolbar
2. Verify the API URL is set to `http://localhost:5173` (or your deployment URL)
3. Ensure "Enable Suggestions" is turned on
4. Click "Test Connection" to verify the extension can communicate with the web app

### 3. Set Up Environment Variables

Make sure your web app has the required environment variable:

```bash
# In your .env file
VITE_OPENROUTER_API_KEY=your_openrouter_api_key_here
```

## Usage

### 1. Navigate to a Supported Dating Platform

Open one of the supported platforms:
- [Tinder Web](https://tinder.com)
- [Bumble Web](https://bumble.com)  
- [Hinge Web](https://hinge.co)

### 2. Start a Conversation

Open any conversation or match you'd like help with.

### 3. Get AI Suggestions

1. **Click the Floating Button**: A red floating action button (💬) will appear on the page
2. **Wait for Analysis**: The extension captures a screenshot and analyzes the conversation context
3. **Review Suggestions**: AI-generated reply suggestions will appear in a panel
4. **Use Suggestions**: Click "Copy" on any suggestion to copy it to your clipboard
5. **Send Your Message**: Paste and send the suggestion, or use it as inspiration

## How It Works

1. **Screenshot Capture**: When you click the floating button, the extension captures a screenshot of the current conversation
2. **Context Analysis**: The screenshot and any visible text are sent to the TinderOP web app
3. **AI Processing**: Gemini 2.5 Flash analyzes the conversation context and generates appropriate responses
4. **Suggestion Display**: Multiple response options are displayed with different tones (witty, sincere, etc.)
5. **Privacy Protection**: The screenshot is immediately deleted after processing

## Privacy & Security

- ✅ **No Permanent Storage**: Screenshots are deleted immediately after AI analysis
- ✅ **Local Processing**: All data stays between your browser and the TinderOP app
- ✅ **No Conversation Logging**: We don't store or log your conversations
- ✅ **User Control**: You control when screenshots are taken - never automatic
- ✅ **Secure Communication**: All data transmission uses HTTPS

## Troubleshooting

### Extension Not Working

1. **Check Extension Status**
   - Go to `chrome://extensions/`
   - Ensure TinderOP Helper is enabled
   - Try disabling and re-enabling the extension

2. **Verify Web App Connection**
   - Make sure the TinderOP web app is running
   - Check the API URL in extension settings
   - Click "Test Connection" in the extension popup

3. **Check Console for Errors**
   - Open Chrome DevTools (F12)
   - Check the Console tab for error messages
   - Look for network errors or API failures

### Floating Button Not Appearing

1. **Refresh the Page**: Try refreshing the dating app page
2. **Check URL**: Ensure you're on a supported platform (tinder.com, bumble.com, hinge.co)
3. **Disable Other Extensions**: Other extensions might conflict - try disabling them temporarily

### API Errors

1. **Check OpenRouter API Key**: Ensure `VITE_OPENROUTER_API_KEY` is set correctly
2. **Verify Web App Status**: Make sure the web app is running without errors
3. **Check Network**: Ensure your internet connection is stable

## Development

### File Structure

```
chrome-extension/
├── manifest.json          # Extension configuration
├── content-script.js      # Runs on dating app pages
├── content-styles.css     # Styles for overlay UI
├── background.js          # Service worker for screenshots
├── popup.html            # Extension popup interface
├── popup.js              # Popup functionality
├── icons/                # Extension icons (TODO)
└── README.md             # This file
```

### Making Changes

1. **Edit Files**: Make changes to any extension files
2. **Reload Extension**: Go to `chrome://extensions/` and click the reload button
3. **Refresh Pages**: Refresh any dating app pages to load changes
4. **Test Changes**: Verify functionality works as expected

### Adding New Platforms

To add support for a new dating platform:

1. **Update Manifest**: Add the new domain to `manifest.json` permissions
2. **Update Content Script**: Add platform detection in `content-script.js`
3. **Test Integration**: Verify the floating button appears and functions work

## Support

- **Issues**: Report bugs and feature requests in the main TinderOP repository
- **Documentation**: Check the main TinderOP documentation for API details
- **Development**: See the main project README for development setup

## License

This extension is part of the TinderOP project and uses the same license.