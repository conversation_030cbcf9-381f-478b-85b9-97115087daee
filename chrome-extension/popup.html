<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>TinderOP Helper</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      width: 320px;
      min-height: 400px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: linear-gradient(135deg, #FF5851 0%, #FF8A80 100%);
      color: white;
    }

    .header {
      padding: 20px;
      text-align: center;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }

    .logo {
      font-size: 24px;
      font-weight: 700;
      margin-bottom: 5px;
    }

    .tagline {
      font-size: 14px;
      opacity: 0.8;
    }

    .content {
      padding: 20px;
    }

    .status-section {
      margin-bottom: 24px;
    }

    .status-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      padding: 12px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
    }

    .status-label {
      font-size: 14px;
      font-weight: 500;
    }

    .status-value {
      font-size: 12px;
      padding: 4px 8px;
      border-radius: 4px;
      background: rgba(255, 255, 255, 0.2);
    }

    .status-value.active {
      background: #4CAF50;
    }

    .status-value.inactive {
      background: #f44336;
    }

    .settings-section {
      margin-bottom: 24px;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 12px;
    }

    .setting-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }

    .setting-label {
      font-size: 14px;
      flex: 1;
    }

    .toggle {
      position: relative;
      width: 44px;
      height: 24px;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 12px;
      cursor: pointer;
      transition: background-color 0.3s ease;
    }

    .toggle.active {
      background: rgba(255, 255, 255, 0.5);
    }

    .toggle::after {
      content: '';
      position: absolute;
      top: 2px;
      left: 2px;
      width: 20px;
      height: 20px;
      background: white;
      border-radius: 50%;
      transition: transform 0.3s ease;
    }

    .toggle.active::after {
      transform: translateX(20px);
    }

    .input-group {
      margin-bottom: 16px;
    }

    .input-label {
      display: block;
      font-size: 14px;
      margin-bottom: 6px;
    }

    .input-field {
      width: 100%;
      padding: 10px 12px;
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 6px;
      background: rgba(255, 255, 255, 0.1);
      color: white;
      font-size: 14px;
    }

    .input-field::placeholder {
      color: rgba(255, 255, 255, 0.6);
    }

    .input-field:focus {
      outline: none;
      border-color: rgba(255, 255, 255, 0.5);
      background: rgba(255, 255, 255, 0.15);
    }

    .actions {
      margin-top: 20px;
    }

    .btn {
      width: 100%;
      padding: 12px;
      border: none;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-bottom: 10px;
    }

    .btn-primary {
      background: white;
      color: #FF5851;
    }

    .btn-primary:hover {
      background: #f5f5f5;
      transform: translateY(-1px);
    }

    .btn-secondary {
      background: rgba(255, 255, 255, 0.2);
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .btn-secondary:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    .footer {
      padding: 15px 20px;
      border-top: 1px solid rgba(255, 255, 255, 0.2);
      text-align: center;
      font-size: 12px;
      opacity: 0.8;
    }

    .loading {
      display: none;
      text-align: center;
      padding: 20px;
    }

    .spinner {
      width: 20px;
      height: 20px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-top: 2px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 10px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .hidden {
      display: none;
    }
  </style>
</head>
<body>
  <div class="header">
    <div class="logo">💬 TinderOP Helper</div>
    <div class="tagline">AI-powered conversation assistant</div>
  </div>

  <div class="content">
    <!-- Status Section -->
    <div class="status-section">
      <div class="section-title">Status</div>
      <div class="status-item">
        <span class="status-label">Connection</span>
        <span class="status-value" id="connection-status">Checking...</span>
      </div>
      <div class="status-item">
        <span class="status-label">Current Page</span>
        <span class="status-value" id="page-status">Unknown</span>
      </div>
    </div>

    <!-- Settings Section -->
    <div class="settings-section">
      <div class="section-title">Settings</div>
      
      <div class="setting-item">
        <span class="setting-label">Enable Suggestions</span>
        <div class="toggle active" id="suggestions-toggle"></div>
      </div>

      <div class="input-group">
        <label class="input-label" for="api-url">API URL</label>
        <input 
          type="text" 
          id="api-url" 
          class="input-field" 
          placeholder="http://localhost:5173"
          value="http://localhost:5173"
        >
      </div>
    </div>

    <!-- Actions -->
    <div class="actions">
      <button class="btn btn-primary" id="save-settings">Save Settings</button>
      <button class="btn btn-secondary" id="test-connection">Test Connection</button>
    </div>

    <!-- Loading State -->
    <div class="loading" id="loading">
      <div class="spinner"></div>
      <div>Loading...</div>
    </div>
  </div>

  <div class="footer">
    Version 1.0.0 • <a href="#" id="help-link" style="color: rgba(255,255,255,0.8);">Help</a>
  </div>

  <script src="popup.js"></script>
</body>
</html>