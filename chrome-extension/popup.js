// TinderOP Helper - Popup Script
// Handles extension popup interface and settings management

class TinderOPPopup {
  constructor() {
    this.config = {};
    this.init();
  }

  async init() {
    try {
      // Load current configuration
      await this.loadConfig();
      
      // Check current page status
      await this.checkPageStatus();
      
      // Test API connection
      await this.testConnection();
      
      // Set up event listeners
      this.setupEventListeners();
      
      console.log('✅ Popup initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize popup:', error);
    }
  }

  async loadConfig() {
    try {
      const response = await this.sendMessageToBackground('getConfig');
      if (response.success) {
        this.config = response.config;
        this.updateUIFromConfig();
      }
    } catch (error) {
      console.error('❌ Failed to load config:', error);
    }
  }

  updateUIFromConfig() {
    // Update API URL input
    const apiUrlInput = document.getElementById('api-url');
    if (this.config.apiUrl) {
      apiUrlInput.value = this.config.apiUrl;
    }

    // Update suggestions toggle
    const suggestionsToggle = document.getElementById('suggestions-toggle');
    if (this.config.suggestionsEnabled !== undefined) {
      suggestionsToggle.classList.toggle('active', this.config.suggestionsEnabled);
    }
  }

  async checkPageStatus() {
    try {
      // Get current active tab
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      if (tab && tab.url) {
        const platform = this.detectPlatform(tab.url);
        const pageStatusElement = document.getElementById('page-status');
        
        if (platform !== 'unknown') {
          pageStatusElement.textContent = platform.charAt(0).toUpperCase() + platform.slice(1);
          pageStatusElement.className = 'status-value active';
        } else {
          pageStatusElement.textContent = 'Not supported';
          pageStatusElement.className = 'status-value inactive';
        }
      }
    } catch (error) {
      console.error('❌ Failed to check page status:', error);
      const pageStatusElement = document.getElementById('page-status');
      pageStatusElement.textContent = 'Error';
      pageStatusElement.className = 'status-value inactive';
    }
  }

  async testConnection() {
    const connectionStatusElement = document.getElementById('connection-status');
    
    try {
      connectionStatusElement.textContent = 'Testing...';
      connectionStatusElement.className = 'status-value';
      
      const apiUrl = this.config.apiUrl || 'http://localhost:5173';
      const response = await fetch(`${apiUrl}/api/health`, {
        method: 'GET',
        timeout: 5000
      });
      
      if (response.ok) {
        connectionStatusElement.textContent = 'Connected';
        connectionStatusElement.className = 'status-value active';
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
    } catch (error) {
      console.error('❌ Connection test failed:', error);
      connectionStatusElement.textContent = 'Offline';
      connectionStatusElement.className = 'status-value inactive';
    }
  }

  detectPlatform(url) {
    if (url.includes('tinder.com')) return 'tinder';
    if (url.includes('bumble.com')) return 'bumble';
    if (url.includes('hinge.co')) return 'hinge';
    return 'unknown';
  }

  setupEventListeners() {
    // Save settings button
    document.getElementById('save-settings').addEventListener('click', () => {
      this.saveSettings();
    });

    // Test connection button
    document.getElementById('test-connection').addEventListener('click', () => {
      this.testConnection();
    });

    // Suggestions toggle
    document.getElementById('suggestions-toggle').addEventListener('click', (e) => {
      e.target.classList.toggle('active');
    });

    // Help link
    document.getElementById('help-link').addEventListener('click', (e) => {
      e.preventDefault();
      this.showHelp();
    });

    // API URL input change
    document.getElementById('api-url').addEventListener('input', () => {
      // Auto-save after 1 second of no typing
      clearTimeout(this.saveTimeout);
      this.saveTimeout = setTimeout(() => {
        this.saveSettings(false); // Silent save
      }, 1000);
    });
  }

  async saveSettings(showFeedback = true) {
    try {
      if (showFeedback) {
        this.showLoading(true);
      }

      // Collect settings from UI
      const apiUrl = document.getElementById('api-url').value.trim();
      const suggestionsEnabled = document.getElementById('suggestions-toggle').classList.contains('active');

      // Validate API URL
      if (apiUrl && !this.isValidUrl(apiUrl)) {
        throw new Error('Invalid API URL format');
      }

      // Create new config
      const newConfig = {
        ...this.config,
        apiUrl: apiUrl || 'http://localhost:5173',
        suggestionsEnabled
      };

      // Save to background script
      const response = await this.sendMessageToBackground('updateConfig', { config: newConfig });
      
      if (response.success) {
        this.config = response.config;
        
        if (showFeedback) {
          this.showSuccess('Settings saved successfully!');
          // Re-test connection with new settings
          setTimeout(() => this.testConnection(), 500);
        }
      } else {
        throw new Error(response.error || 'Failed to save settings');
      }

    } catch (error) {
      console.error('❌ Failed to save settings:', error);
      if (showFeedback) {
        this.showError(error.message || 'Failed to save settings');
      }
    } finally {
      if (showFeedback) {
        this.showLoading(false);
      }
    }
  }

  isValidUrl(string) {
    try {
      new URL(string);
      return true;
    } catch (_) {
      return false;
    }
  }

  async sendMessageToBackground(action, data = {}) {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage({ action, ...data }, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(response);
        }
      });
    });
  }

  showLoading(show) {
    const loadingElement = document.getElementById('loading');
    const contentElement = document.querySelector('.content');
    
    if (show) {
      loadingElement.style.display = 'block';
      contentElement.style.opacity = '0.5';
    } else {
      loadingElement.style.display = 'none';
      contentElement.style.opacity = '1';
    }
  }

  showSuccess(message) {
    this.showTemporaryMessage(message, 'success');
  }

  showError(message) {
    this.showTemporaryMessage(message, 'error');
  }

  showTemporaryMessage(message, type) {
    // Create temporary message element
    const messageElement = document.createElement('div');
    messageElement.style.cssText = `
      position: fixed;
      top: 10px;
      left: 10px;
      right: 10px;
      padding: 12px;
      border-radius: 6px;
      font-size: 14px;
      text-align: center;
      z-index: 1000;
      animation: slideDown 0.3s ease;
      ${type === 'success' 
        ? 'background: #4CAF50; color: white;' 
        : 'background: #f44336; color: white;'
      }
    `;
    messageElement.textContent = message;
    
    // Add CSS animation
    const style = document.createElement('style');
    style.textContent = `
      @keyframes slideDown {
        from { transform: translateY(-100%); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
      }
    `;
    document.head.appendChild(style);
    
    document.body.appendChild(messageElement);
    
    // Remove after 3 seconds
    setTimeout(() => {
      messageElement.remove();
      style.remove();
    }, 3000);
  }

  showHelp() {
    // Create help modal or navigate to help page
    const helpContent = `
TinderOP Helper - Quick Guide:

1. Make sure you're on a supported dating platform (Tinder, Bumble, or Hinge)
2. Click the floating button that appears on the page
3. The extension will capture a screenshot and analyze your conversation
4. Get AI-powered suggestions for your next message
5. Click "Copy" to use a suggestion

Settings:
• API URL: The TinderOP web app location (usually localhost:5173 for development)
• Enable Suggestions: Turn on/off the suggestion feature

Need more help? Check the TinderOP documentation.
    `;
    
    alert(helpContent);
  }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new TinderOPPopup();
});