/* TinderOP Helper - Content Script Styles */

/* Overlay Container */
.tinderop-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 999999;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Floating Action Button */
.tinderop-fab {
  position: fixed;
  bottom: 100px;
  right: 20px;
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #FF5851 0%, #FF8A80 100%);
  border: none;
  border-radius: 50%;
  box-shadow: 0 4px 12px rgba(255, 88, 81, 0.4);
  cursor: pointer;
  pointer-events: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  transition: all 0.3s ease;
  z-index: 1000000;
}

.tinderop-fab:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(255, 88, 81, 0.6);
}

.tinderop-fab.active {
  background: linear-gradient(135deg, #FF8A80 0%, #FF5851 100%);
  transform: scale(1.05);
}

.tinderop-fab svg {
  width: 24px;
  height: 24px;
}

/* Suggestion Panel */
.tinderop-suggestions {
  position: fixed;
  bottom: 170px;
  right: 20px;
  width: 350px;
  max-height: 500px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.08);
  pointer-events: auto;
  overflow: hidden;
  transition: all 0.3s ease;
  z-index: 1000001;
}

.tinderop-suggestions.hidden {
  opacity: 0;
  transform: translateY(20px) scale(0.95);
  pointer-events: none;
}

.suggestions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  background: linear-gradient(135deg, #FF5851 0%, #FF8A80 100%);
  color: white;
}

.suggestions-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  font-weight: 300;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.suggestions-content {
  max-height: 400px;
  overflow-y: auto;
}

/* Loading State */
.loading-state {
  padding: 32px 20px;
  text-align: center;
  color: #666;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #FF5851;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-state p {
  margin: 0;
  font-size: 14px;
}

/* Suggestions List */
.suggestions-list {
  padding: 16px;
}

.suggestion-item {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;
}

.suggestion-item:hover {
  background: #f0f1f2;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.suggestion-item:last-child {
  margin-bottom: 0;
}

.suggestion-text {
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  margin-bottom: 12px;
  word-wrap: break-word;
}

.suggestion-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tone {
  font-size: 12px;
  font-weight: 500;
  color: #666;
  background: rgba(255, 88, 81, 0.1);
  padding: 4px 8px;
  border-radius: 6px;
  text-transform: capitalize;
}

.copy-btn {
  background: #FF5851;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.copy-btn:hover {
  background: #e04e47;
  transform: scale(1.05);
}

.copy-btn:active {
  transform: scale(0.95);
}

/* Error State */
.error-message {
  padding: 20px;
  text-align: center;
  color: #e74c3c;
  font-size: 14px;
  margin: 0;
}

.no-suggestions {
  padding: 20px;
  text-align: center;
  color: #666;
  font-size: 14px;
  margin: 0;
}

/* Mobile Responsiveness */
@media (max-width: 480px) {
  .tinderop-suggestions {
    right: 10px;
    left: 10px;
    width: auto;
    bottom: 140px;
  }
  
  .tinderop-fab {
    right: 15px;
    bottom: 80px;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .tinderop-suggestions {
    background: #2a2a2a;
    border-color: rgba(255, 255, 255, 0.1);
    color: white;
  }
  
  .suggestions-header {
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }
  
  .suggestion-item {
    background: #3a3a3a;
    border-color: rgba(255, 255, 255, 0.1);
    color: white;
  }
  
  .suggestion-item:hover {
    background: #4a4a4a;
  }
  
  .suggestion-text {
    color: #e0e0e0;
  }
  
  .tone {
    background: rgba(255, 88, 81, 0.2);
    color: #ffb3b3;
  }
  
  .loading-state {
    color: #ccc;
  }
  
  .no-suggestions,
  .error-message {
    color: #ccc;
  }
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.suggestion-item {
  animation: fadeInUp 0.3s ease forwards;
}

.suggestion-item:nth-child(2) { animation-delay: 0.1s; }
.suggestion-item:nth-child(3) { animation-delay: 0.2s; }
.suggestion-item:nth-child(4) { animation-delay: 0.3s; }

/* Scrollbar Styling */
.suggestions-content::-webkit-scrollbar {
  width: 6px;
}

.suggestions-content::-webkit-scrollbar-track {
  background: transparent;
}

.suggestions-content::-webkit-scrollbar-thumb {
  background: rgba(255, 88, 81, 0.3);
  border-radius: 3px;
}

.suggestions-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 88, 81, 0.5);
}