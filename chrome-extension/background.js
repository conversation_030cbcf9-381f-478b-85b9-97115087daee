// TinderOP Helper - Background Script (Service Worker)
// Handles screenshot capture and communication between content script and web app

class TinderOPBackgroundService {
  constructor() {
    this.setupMessageListener();
    this.setupInstallListener();
    console.log('🔧 TinderOP Helper background service initialized');
  }

  setupMessageListener() {
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      console.log('📨 Received message:', request.action);
      
      switch (request.action) {
        case 'captureScreenshot':
          this.handleScreenshotRequest(sender.tab, sendResponse);
          return true; // Keep message channel open for async response
          
        case 'getConfig':
          this.handleConfigRequest(sendResponse);
          return true;
          
        case 'updateConfig':
          this.handleConfigUpdate(request.config, sendResponse);
          return true;
          
        default:
          console.warn('⚠️ Unknown action:', request.action);
          sendResponse({ error: 'Unknown action' });
      }
    });
  }

  setupInstallListener() {
    chrome.runtime.onInstalled.addListener((details) => {
      console.log('🎉 TinderOP Helper installed:', details.reason);
      
      if (details.reason === 'install') {
        this.initializeExtension();
      } else if (details.reason === 'update') {
        this.handleExtensionUpdate();
      }
    });
  }

  async initializeExtension() {
    try {
      // Set default configuration
      const defaultConfig = {
        apiUrl: 'http://localhost:5173',
        autoCapture: false,
        suggestionsEnabled: true,
        maxRetries: 3,
        version: '1.0.0'
      };
      
      await chrome.storage.local.set({ tinderopConfig: defaultConfig });
      console.log('✅ Extension initialized with default config');
      
      // Show welcome notification (optional)
      this.showWelcomeNotification();
      
    } catch (error) {
      console.error('❌ Failed to initialize extension:', error);
    }
  }

  async handleExtensionUpdate() {
    try {
      const data = await chrome.storage.local.get('tinderopConfig');
      const config = data.tinderopConfig || {};
      
      // Update version
      config.version = '1.0.0';
      
      await chrome.storage.local.set({ tinderopConfig: config });
      console.log('🔄 Extension updated successfully');
      
    } catch (error) {
      console.error('❌ Failed to handle extension update:', error);
    }
  }

  async handleScreenshotRequest(tab, sendResponse) {
    try {
      console.log('📸 Capturing screenshot for tab:', tab.id);
      
      // Check if tab is valid and has appropriate permissions
      if (!tab || !tab.id) {
        throw new Error('Invalid tab');
      }
      
      // Capture visible tab
      const screenshot = await chrome.tabs.captureVisibleTab(tab.windowId, {
        format: 'png',
        quality: 90
      });
      
      // Process the screenshot to focus on conversation area
      const processedScreenshot = await this.processScreenshot(screenshot, tab);
      
      console.log('✅ Screenshot captured successfully');
      sendResponse({ 
        success: true,
        screenshot: processedScreenshot,
        timestamp: Date.now()
      });
      
    } catch (error) {
      console.error('❌ Screenshot capture failed:', error);
      sendResponse({ 
        error: error.message || 'Failed to capture screenshot',
        timestamp: Date.now()
      });
    }
  }

  async processScreenshot(screenshot, tab) {
    try {
      // For now, return the original screenshot
      // In the future, we could add image processing to crop to conversation area
      
      // Remove data URL prefix to get base64 string
      const base64Data = screenshot.replace(/^data:image\/png;base64,/, '');
      
      console.log(`📏 Screenshot processed: ${base64Data.length} characters`);
      return base64Data;
      
    } catch (error) {
      console.error('❌ Screenshot processing failed:', error);
      throw error;
    }
  }

  async handleConfigRequest(sendResponse) {
    try {
      const data = await chrome.storage.local.get('tinderopConfig');
      const config = data.tinderopConfig || {};
      
      sendResponse({ 
        success: true,
        config: config
      });
      
    } catch (error) {
      console.error('❌ Failed to get config:', error);
      sendResponse({ 
        error: 'Failed to get configuration'
      });
    }
  }

  async handleConfigUpdate(newConfig, sendResponse) {
    try {
      const data = await chrome.storage.local.get('tinderopConfig');
      const currentConfig = data.tinderopConfig || {};
      
      const updatedConfig = { ...currentConfig, ...newConfig };
      await chrome.storage.local.set({ tinderopConfig: updatedConfig });
      
      console.log('⚙️ Configuration updated:', updatedConfig);
      sendResponse({ 
        success: true,
        config: updatedConfig
      });
      
    } catch (error) {
      console.error('❌ Failed to update config:', error);
      sendResponse({ 
        error: 'Failed to update configuration'
      });
    }
  }

  showWelcomeNotification() {
    // Could show a welcome notification, but keeping it simple for now
    console.log('👋 Welcome to TinderOP Helper! Click the floating button on dating apps to get conversation suggestions.');
  }

  // Utility method to check if URL is a supported dating platform
  isSupportedPlatform(url) {
    const supportedDomains = [
      'tinder.com',
      'bumble.com',
      'hinge.co'
    ];
    
    return supportedDomains.some(domain => url.includes(domain));
  }

  // Method to handle tab updates (could be used for auto-detection)
  handleTabUpdate(tabId, changeInfo, tab) {
    if (changeInfo.status === 'complete' && tab.url) {
      if (this.isSupportedPlatform(tab.url)) {
        console.log('🎯 Detected supported dating platform:', tab.url);
        // Could inject content script or send notification
      }
    }
  }
}

// Initialize the background service
const tinderOPService = new TinderOPBackgroundService();

// Optional: Listen for tab updates to detect dating platforms
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  tinderOPService.handleTabUpdate(tabId, changeInfo, tab);
});

// Keep service worker alive
chrome.runtime.onStartup.addListener(() => {
  console.log('🚀 TinderOP Helper service worker started');
});

// Handle extension suspend/resume
self.addEventListener('activate', (event) => {
  console.log('🔄 TinderOP Helper service worker activated');
});

// Error handling for unhandled promises
self.addEventListener('unhandledrejection', (event) => {
  console.error('❌ Unhandled promise rejection in background script:', event.reason);
});

// Periodic cleanup (optional)
setInterval(() => {
  // Could clean up old data, check for updates, etc.
  console.log('🧹 Background service heartbeat');
}, 300000); // Every 5 minutes